import { ref, onMounted, onUnmounted } from 'vue'
import {
  preloadImages,
  getOptimizedImageSrc,
  ImageLazyLoader,
  supportsWebP
} from '@/utils/imageOptimization'

/**
 * 图片优化 composable
 * @param {Array<string>} imageSources - 图片源数组
 * @param {Object} options - 优化选项
 */
export function useImageOptimization(imageSources = [], options = {}) {
  const {
    preload = true,
    optimize = true,
    quality = 80,
    format = 'auto',
    lazy = false
  } = options

  // 响应式数据
  const optimizedSources = ref([])
  const isLoading = ref(false)
  const isWebPSupported = ref(false)
  const lazyLoader = ref(null)

  // 检测 WebP 支持
  const checkWebPSupport = async () => {
    isWebPSupported.value = await supportsWebP()
  }

  // 优化图片源
  const optimizeImageSources = async () => {
    if (!optimize) {
      optimizedSources.value = imageSources
      return
    }

    isLoading.value = true

    try {
      const optimized = await Promise.all(
        imageSources.map(src =>
          getOptimizedImageSrc(src, { format, quality })
        )
      )
      optimizedSources.value = optimized
    } catch (error) {
      console.warn('图片优化失败，使用原始图片:', error)
      optimizedSources.value = imageSources
    } finally {
      isLoading.value = false
    }
  }

  // 预加载图片
  const preloadOptimizedImages = async () => {
    if (!preload || optimizedSources.value.length === 0) return

    try {
      await preloadImages(optimizedSources.value)
      console.log('图片预加载完成')
    } catch (error) {
      console.warn('图片预加载失败:', error)
    }
  }

  // 初始化懒加载
  const initLazyLoading = () => {
    if (!lazy) return

    lazyLoader.value = new ImageLazyLoader({
      rootMargin: '50px',
      threshold: 0.1
    })
  }

  // 添加懒加载观察
  const observeLazyImage = (element) => {
    if (lazyLoader.value && element) {
      lazyLoader.value.observe(element)
    }
  }

  // 移除懒加载观察
  const unobserveLazyImage = (element) => {
    if (lazyLoader.value && element) {
      lazyLoader.value.unobserve(element)
    }
  }

  // 初始化
  const initialize = async () => {
    await checkWebPSupport()
    await optimizeImageSources()

    if (preload) {
      await preloadOptimizedImages()
    }

    if (lazy) {
      initLazyLoading()
    }
  }

  // 生命周期钩子
  onMounted(() => {
    initialize()
  })

  onUnmounted(() => {
    if (lazyLoader.value) {
      lazyLoader.value.disconnect()
    }
  })

  return {
    optimizedSources,
    isLoading,
    isWebPSupported,
    observeLazyImage,
    unobserveLazyImage,
    initialize
  }
}

/**
 * 单个图片优化 composable
 * @param {string} imageSrc - 图片源
 * @param {Object} options - 优化选项
 */
export function useSingleImageOptimization(imageSrc, options = {}) {
  const optimizedSrc = ref(imageSrc)
  const isLoading = ref(false)
  const error = ref(null)

  const {
    preload = true,
    quality = 80,
    format = 'auto'
  } = options

  const optimize = async () => {
    if (!imageSrc) return

    isLoading.value = true
    error.value = null

    try {
      const optimized = await getOptimizedImageSrc(imageSrc, { format, quality })
      optimizedSrc.value = optimized

      if (preload) {
        await preloadImages([optimized])
      }
    } catch (err) {
      error.value = err
      console.warn('图片优化失败:', err)
    } finally {
      isLoading.value = false
    }
  }

  onMounted(() => {
    optimize()
  })

  return {
    optimizedSrc,
    isLoading,
    error,
    optimize
  }
}

/**
 * 响应式图片 composable
 * @param {string} baseSrc - 基础图片路径
 * @param {Array<number>} breakpoints - 断点数组
 */
export function useResponsiveImage(baseSrc, breakpoints = [480, 768, 1024, 1440]) {
  const currentSrc = ref(baseSrc)
  const srcset = ref('')

  const generateSrcset = () => {
    const extension = baseSrc.split('.').pop()
    const baseName = baseSrc.replace(`.${extension}`, '')

    srcset.value = breakpoints
      .map(size => `${baseName}_${size}w.${extension} ${size}w`)
      .join(', ')
  }

  const updateCurrentSrc = () => {
    const width = window.innerWidth
    const targetBreakpoint = breakpoints.find(bp => width <= bp) || breakpoints[breakpoints.length - 1]

    const extension = baseSrc.split('.').pop()
    const baseName = baseSrc.replace(`.${extension}`, '')
    currentSrc.value = `${baseName}_${targetBreakpoint}w.${extension}`
  }

  onMounted(() => {
    generateSrcset()
    updateCurrentSrc()

    window.addEventListener('resize', updateCurrentSrc)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateCurrentSrc)
  })

  return {
    currentSrc,
    srcset
  }
}
