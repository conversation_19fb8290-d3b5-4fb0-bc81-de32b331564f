/**
 * 资源相关API服务
 */

import request from "@/utils/request"
import { RESOURCE_API } from "./constants"

/**
 * 资源API服务类
 */
export class ResourceService {
  /**
   * 获取资源文件（图片、头像等）
   * @param {string} resourcePath 资源路径，如 'chat/image/1944638070133493760.png' 或 'user/avatar/1943552366045433856.gif'
   * @param {Object} options 请求选项
   * @param {string} options.responseType 响应类型，默认为 'blob'
   * @param {boolean} options.silent 是否静默请求，默认为 true
   * @returns {Promise} 资源数据
   */
  static async getResource(resourcePath, options = {}) {
    try {
      // 参数验证
      if (!resourcePath) {
        throw new Error("资源路径不能为空")
      }

      // 默认选项
      const {
        responseType = "blob",
        silent = true
      } = options

      // 构建完整的资源URL
      // 确保路径前面有斜杠
      const path = resourcePath.startsWith('/') ? resourcePath : `/${resourcePath}`
      const resourceUrl = `${RESOURCE_API.GET_RESOURCE}${path}`

      console.log('请求资源URL:', resourceUrl) // 调试日志

      // 发起请求
      const response = await request({
        method: "get",
        url: resourceUrl,
        responseType: responseType,
        headers: silent ? {
          "X-Silent-Request": "true", // 静默请求，不显示成功提示
        } : {},
      })

      // 如果是blob类型，创建可显示的URL
      if (responseType === "blob" && response instanceof Blob) {
        const blobUrl = URL.createObjectURL(response)

        return {
          success: true,
          data: {
            blob: response,
            url: blobUrl,
            path: resourcePath,
            size: response.size,
            type: response.type,
          },
          message: "获取资源成功",
        }
      } else {
        // 其他响应类型直接返回
        return {
          success: true,
          data: response,
          message: "获取资源成功",
        }
      }
    } catch (error) {
      console.error("获取资源错误:", error)
      return {
        success: false,
        error: error.message,
        message: "获取资源失败",
      }
    }
  }

  /**
   * 获取聊天图片
   * @param {string} imagePath 图片路径，如 'chat/image/1944638070133493760.png'
   * @returns {Promise} 图片数据
   */
  static async getChatImage(imagePath) {
    return await this.getResource(imagePath, {
      responseType: "blob",
      silent: true
    })
  }

  /**
   * 获取用户头像
   * @param {string} avatarPath 头像路径，如 'user/avatar/1943552366045433856.gif'
   * @returns {Promise} 头像数据
   */
  static async getUserAvatar(avatarPath) {
    return await this.getResource(avatarPath, {
      responseType: "blob",
      silent: true
    })
  }

  /**
   * 清理blob URL（释放内存）
   * @param {string} blobUrl blob URL
   */
  static revokeBlobUrl(blobUrl) {
    if (blobUrl && blobUrl.startsWith("blob:")) {
      URL.revokeObjectURL(blobUrl)
    }
  }

  /**
   * 批量清理blob URLs
   * @param {Array<string>} blobUrls blob URL数组
   */
  static revokeBlobUrls(blobUrls) {
    if (Array.isArray(blobUrls)) {
      blobUrls.forEach(url => this.revokeBlobUrl(url))
    }
  }

  /**
   * 预加载资源
   * @param {string} resourcePath 资源路径
   * @returns {Promise} 预加载结果
   */
  static async preloadResource(resourcePath) {
    try {
      const result = await this.getResource(resourcePath)
      if (result.success) {
        // 预加载成功，但不需要立即使用，可以缓存起来
        return {
          success: true,
          data: result.data,
          message: "资源预加载成功",
        }
      } else {
        throw new Error(result.error || "预加载失败")
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: "资源预加载失败",
      }
    }
  }

  /**
   * 检查资源是否存在
   * @param {string} resourcePath 资源路径
   * @returns {Promise} 检查结果
   */
  static async checkResourceExists(resourcePath) {
    try {
      const result = await this.getResource(resourcePath, {
        responseType: "blob",
        silent: true
      })
      
      return {
        success: true,
        exists: result.success,
        message: result.success ? "资源存在" : "资源不存在",
      }
    } catch (error) {
      return {
        success: true,
        exists: false,
        message: "资源不存在",
      }
    }
  }
}

// 导出默认实例
export default ResourceService
