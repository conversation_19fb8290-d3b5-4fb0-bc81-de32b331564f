/**
 * plugins/webfontloader.js
 *
 * webfontloader documentation: https://github.com/typekit/webfontloader
 */

export async function loadFonts() {
  const webFontLoader = await import(/* webpackChunkName: "webfontloader" */ "webfontloader")

  webFontLoader.load({
    google: {
      families: [
        "Roboto:100,300,400,500,700,900&display=swap",
        "Inter:100,200,300,400,500,600,700,800,900&display=swap",
        "Noto+Sans+SC:100,300,400,500,700,900&display=swap"
      ],
    },
    // 字体加载完成后的回调
    active: function() {
      console.log('✅ 字体加载完成')
      // 可以在这里触发字体加载完成的事件
      document.documentElement.classList.add('fonts-loaded')
    },
    // 字体加载失败的回调
    inactive: function() {
      console.warn('⚠️ 字体加载失败，使用系统默认字体')
      document.documentElement.classList.add('fonts-failed')
    }
  })
}
