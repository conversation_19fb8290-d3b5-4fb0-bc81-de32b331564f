<script setup>
import { ref, computed } from "vue"
import { useRouter } from "vue-router"
import { useAuthStore } from "@/stores/authstore"
import logoUrl from "@/assets/logo.png"
import logoSvg from "@/assets/icons/logo.svg"
import ErrorDialog from "@/components/common/ErrorDialog.vue"

// 路由和状态管理
const router = useRouter()
const authStore = useAuthStore()

// 表单数据
const form = ref({
  username: "",
  password: "",
})

// 表单验证状态
const showPassword = ref(false)
const rememberMe = ref(true)

// 错误弹窗状态
const showErrorDialog = ref(false)

// 计算属性
const isFormValid = computed(() => {
  return form.value.username.trim() !== "" && form.value.password.trim() !== ""
})

// 登录方法
const handleLogin = async () => {
  if (!isFormValid.value) return

  const result = await authStore.login({
    username: form.value.username,
    password: form.value.password,
  })

  if (result.success) {
    router.push({ name: "home" })
  } else {
    // 显示错误弹窗而不是使用 authStore.error
    showErrorDialog.value = true
    console.error("登录失败:", result.error)
  }
}

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 处理错误弹窗关闭
const handleErrorDialogClose = () => {
  showErrorDialog.value = false
  // 清除 authStore 中的错误信息
  authStore.clearError()
}
</script>

<template>
  <div class="login-page">
    <div class="left-section">
      <div class="illustration-container">
        <div class="character-illustration">
          <img src="@/assets/images/ailogin.png" alt="登录页面插图" class="demo-image" />
        </div>
      </div>
    </div>

    <div class="right-section">
      <div class="login-form-container">
        <div class="brand-section">
          <div class="brand-logo">
            <img :src="logoUrl" alt="Dolphin AI Logo" class="logo-image" />
            <img :src="logoSvg" alt="Dolphin AI" class="logo-svg" />
          </div>
          <h1 class="brand-title">医疗服务模型平台</h1>
          <p class="brand-subtitle">来自于海豚之声研发部，一群执着探索AI医疗的小分队</p>
        </div>

        <VForm class="login-form" @submit.prevent="handleLogin">
          <VTextField
            v-model="form.username"
            label="请输入用户名"
            variant="filled"
            class="form-field"
            hide-details />

          <!-- 密码输入框 -->
          <VTextField
            v-model="form.password"
            :type="showPassword ? 'text' : 'password'"
            label="请输入密码"
            variant="filled"
            class="form-field"
            hide-details
            :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
            @click:append-inner="togglePasswordVisibility" />

          <!-- 记住密码选项 -->
          <VCheckbox v-model="rememberMe" color="#4a90e2" class="remember-checkbox" hide-details>
            <template #label>
              <span class="checkbox-label">
                记住密码，同意《<span class="link-text">隐私政策</span>》和《<span class="link-text"
                  >用户协议</span
                >》
              </span>
            </template>
          </VCheckbox>

          <!-- 登录按钮 -->
          <VBtn
            type="submit"
            color="primary"
            size="large"
            block
            :loading="authStore.loading"
            :disabled="!isFormValid"
            class="login-btn">
            登录
          </VBtn>
        </VForm>
      </div>
    </div>

    <!-- 错误提示弹窗 -->
    <ErrorDialog
      v-model="showErrorDialog"
      title="登录失败"
      :message="authStore.error"
      type="error"
      @close="handleErrorDialogClose" />
  </div>
</template>

<style scoped>
.login-page {
  display: flex;
  height: 100vh;
  width: 100%;
  /* 确保登录页面不受全局主题影响 */
  isolation: isolate;
  position: relative;
  z-index: 1;
}

/* 左侧插图区域 */
.left-section {
  flex: 0.7;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.illustration-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.character-illustration {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.demo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0;
}

/* 右侧登录表单区域 */
.right-section {
  flex: 1;
  background: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.login-form-container {
  width: 100%;
  max-width: 400px;
}

.brand-section {
  text-align: left;
  margin-bottom: 2rem;
}

.brand-logo {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.logo-image {
  width: 48px;
  height: 48px;
  margin-right: 0.75rem;
  object-fit: contain;
}

.logo-svg {
  height: 32px;
  width: auto;
  margin-left: 0.75rem;
  object-fit: contain;
  /* 在登录页面的暗色背景下增加亮度 */
  filter: brightness(1.2) contrast(1.1) saturate(1.1);
}

.brand-title {
  color: #ffffff;
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0.5rem 0;
}

.brand-subtitle {
  color: #9ca3af;
  font-size: 0.9rem;
  font-weight: 400;
  margin: 0;
}

.login-form {
  width: 100%;
}

.form-field {
  margin-bottom: 1.5rem;
}

/* 使用更高优先级确保样式生效 */
.login-page .form-field :deep(.v-field) {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.login-page .form-field :deep(.v-field--focused) {
  background: rgba(255, 255, 255, 0.08) !important;
  border-color: #4a90e2 !important;
  border-width: 2px !important;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2) !important;
}

/* 移除 filled 变体的默认下划线 */
.login-page .form-field :deep(.v-field__underlay) {
  display: none !important;
}

/* 移除任何轮廓线 */
.login-page .form-field :deep(.v-field__outline) {
  display: none !important;
}

.login-page .form-field :deep(.v-label) {
  color: rgba(255, 255, 255, 0.7) !important;
}

.login-page .form-field :deep(.v-field__input) {
  color: white !important;
}

.login-page .form-field :deep(.v-icon) {
  color: rgba(255, 255, 255, 0.7) !important;
}

.remember-checkbox {
  margin-bottom: 2rem;
}

.remember-checkbox :deep(.v-selection-control__input) {
  color: #4a90e2;
}

.remember-checkbox :deep(.v-checkbox .v-selection-control__input .v-icon) {
  color: #4a90e2;
}

.checkbox-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
}

.link-text {
  color: #4a90e2;
  text-decoration: underline;
  cursor: pointer;
}

.link-text:hover {
  color: #357abd;
}

.login-btn {
  background: #4a90e2 !important;
  color: white !important;
  border-radius: 8px !important;
  height: 48px !important;
  font-size: 1rem !important;
  font-weight: 500 !important;
  text-transform: none !important;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.2) !important;
  /* 确保按钮样式不被全局主题覆盖 */
  border: none !important;
  outline: none !important;
}

.login-btn:hover {
  background: #357abd !important;
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.3) !important;
  color: white !important;
}

.login-btn:disabled {
  background: rgba(74, 144, 226, 0.5) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  box-shadow: none !important;
}

.login-btn:focus {
  background: #4a90e2 !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.2), 0 0 0 2px rgba(74, 144, 226, 0.3) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-page {
    flex-direction: column;
  }

  .left-section {
    height: 40vh;
  }

  .right-section {
    height: 60vh;
    padding: 1rem;
  }

  .demo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
