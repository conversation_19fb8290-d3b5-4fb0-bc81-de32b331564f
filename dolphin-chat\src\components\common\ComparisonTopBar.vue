<template>
  <div class="comparison-top-bar">
    <!-- 左侧模型选择器区域 -->
    <div class="left-model-section">
      <VIcon icon="mdi-robot" size="20" color="primary" class="section-icon" />
      <span class="section-title">左侧模型</span>
      <ComparisonModelSelector side="left" class="model-selector" />
    </div>

    <!-- 中间分隔线 -->
    <div class="center-divider">
      <VIcon icon="mdi-compare" size="24" color="grey-darken-1" />
    </div>

    <!-- 右侧模型选择器区域 -->
    <div class="right-model-section">
      <VIcon icon="mdi-robot-outline" size="20" color="secondary" class="section-icon" />
      <span class="section-title">右侧模型</span>
      <ComparisonModelSelector side="right" class="model-selector" />
    </div>

    <!-- 退出对比按钮 - 最右侧 -->
    <div class="exit-section">
      <VBtn
        icon="mdi-close"
        size="small"
        variant="text"
        color="grey-darken-1"
        class="exit-btn"
        @click="chatStore.toggleComparisonMode()"
        title="退出对比模式">
      </VBtn>
    </div>
  </div>
</template>

<script setup>
import { useChatStore } from "@/stores/baseStore"
import ComparisonModelSelector from "./ComparisonModelSelector.vue"

const chatStore = useChatStore()
</script>

<style scoped>
.comparison-top-bar {
  display: flex;
  height: 64px;
  background: var(--app-bg-primary);
  border-bottom: 1px solid var(--app-border-color);
  box-shadow: 0 2px 8px var(--app-shadow-light);
  position: relative;
  z-index: 100;
}

/* 左侧模型区域 - 严格50% */
.left-model-section {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-right: 1px solid var(--app-border-color);
  min-width: 0; /* 防止内容溢出 */
}

/* 中间分隔线 */
.center-divider {
  width: 1px;
  background: var(--app-border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
}

.center-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  background: var(--app-bg-primary);
  border: 1px solid var(--app-border-color);
  border-radius: 50%;
  z-index: 1;
}

.center-divider .v-icon {
  position: relative;
  z-index: 2;
  background: var(--app-bg-primary);
  padding: 4px;
  border-radius: 50%;
}

/* 右侧模型区域 - 严格50% */
.right-model-section {
  flex: 0.91;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  min-width: 0; /* 防止内容溢出 */
}

/* 退出按钮区域 - 最右侧 */
.exit-section {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  flex-shrink: 0;
}

/* 模型选择器样式 */
.model-selector {
  flex: 1;
  max-width: 200px; /* 限制选择框最大宽度 */
  min-width: 150px; /* 确保最小宽度 */
}

.section-icon {
  flex-shrink: 0;
}

.section-title {
  font-size: 12px;
  font-weight: 500;
  color: var(--app-text-secondary);
  white-space: nowrap;
  flex-shrink: 0;
}

/* 退出按钮 */
.exit-btn {
  flex-shrink: 0;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.exit-btn:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.04);
}

/* 确保模型选择器按钮样式 */
:deep(.model-selector-btn) {
  min-height: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comparison-top-bar {
    height: auto;
    min-height: 64px;
    flex-direction: column;
  }
  
  .left-model-section,
  .right-model-section {
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .center-divider {
    width: 100%;
    height: 1px;
    order: 1;
  }
  
  .right-model-section {
    order: 2;
    border-bottom: none;
  }
}

/* 深色主题支持 */
[data-theme="dark"] .comparison-top-bar {
  background: var(--app-bg-primary);
  border-bottom-color: var(--app-border-color);
}

[data-theme="dark"] .left-model-section {
  border-right-color: var(--app-border-color);
}

[data-theme="dark"] .center-divider {
  background: var(--app-border-color);
}

[data-theme="dark"] .center-divider::before {
  background: var(--app-bg-primary);
  border-color: var(--app-border-color);
}

[data-theme="dark"] .center-divider .v-icon {
  background: var(--app-bg-primary);
}

[data-theme="dark"] .section-title {
  color: var(--app-text-secondary);
}
</style>
