<script setup>
import { ref, computed, watch, inject, onMounted, onUnmounted } from "vue"
import { useRouter } from "vue-router"
import logoUrl from "@/assets/logo.png"
import appIconUrl from "@/assets/icons/app.svg"
// 导入子组件
import SidebarBrand from "./sidebar/header/SidebarBrand.vue"
import SidebarToggle from "./sidebar/header/SidebarToggle.vue"
import SidebarNewChat from "./sidebar/header/SidebarNewChat.vue"
import ConversationHistory from "@/pages/conversation/ConversationHistory.vue"
import UserProfileMenu from "@/pages/user/components/UserProfileMenu.vue"
import SystemSettingsDialog from "@/pages/settings/SystemSettingsDialog.vue"
// 导入聊天store
import { useChatStore } from "@/stores/baseStore"

// 定义emits
const emit = defineEmits(["sidebar-toggle"])

// 路由
const router = useRouter()

// 注入来自父组件的侧边栏状态
const parentSidebarCollapsed = inject("sidebarCollapsed", ref(false))

// 响应式数据
const isCollapsed = ref(false) // 侧边栏收缩状态

// 监听父组件的侧边栏状态变化
watch(
  parentSidebarCollapsed,
  (newValue) => {
    isCollapsed.value = newValue
  },
  { immediate: true }
)

// 使用聊天store
const chatStore = useChatStore()

// 会话历史组件引用
const conversationHistoryRef = ref(null)

// 方法
const handleConversationSelect = async (conversation) => {
  if (!conversation || !conversation.id) {
    console.error("❌ 会话选择失败：会话数据无效")
    return
  }

  try {
    // 调用chatStore加载会话消息
    const result = await chatStore.loadConversationMessages(conversation.id)

    if (result.success) {
      console.log("✅ 会话切换成功，消息数量:", result.data?.length || 0)
    } else {
      console.error("❌ 会话切换失败:", result.message)
      // 可以在这里显示错误提示
    }
  } catch (error) {
    console.error("❌ 会话切换异常:", error)
  }
}

// 处理会话创建成功
const handleSessionCreated = (sessionId) => {
  // 调用会话历史组件的刷新方法
  if (
    conversationHistoryRef.value &&
    typeof conversationHistoryRef.value.fetchSessions === "function"
  ) {
    conversationHistoryRef.value.fetchSessions()
  }
}

// 处理对话重命名
const handleConversationRename = (data) => {
  // 这里可以调用API来保存重命名
  // ChatAPI.renameConversation(data.id, data.newTitle)
}

// 处理对话删除
const handleConversationDelete = (conversationId) => {
  console.log("🗑️ 侧边栏: 处理会话删除:", conversationId)

  // 调用 chatStore 的删除方法来处理会话删除
  chatStore.deleteConversation(conversationId)
}

// 系统设置弹窗状态
const settingsDialogOpen = ref(false)

// 移动端App提示状态
const showMobileAppTip = ref(false)

const openMobileApp = () => {
  // 显示移动端App开发中的提示
  showMobileAppTip.value = true
}

const handleSettingsOpen = () => {
  settingsDialogOpen.value = true
}

const handleToggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
  // 同步更新父组件的状态
  parentSidebarCollapsed.value = isCollapsed.value
  emit("sidebar-toggle", isCollapsed.value)
}

const handleLogoClick = () => {
  // 可以添加跳转到首页或其他功能
  // router.push({ name: "home" })
}

// 监听自动创建会话事件
const handleAutoSessionCreated = (event) => {
  const { sessionId } = event.detail
  console.log("🔄 收到自动创建会话事件，刷新对话列表，sessionId:", sessionId)
  handleSessionCreated(sessionId)
}

// 生命周期钩子
onMounted(() => {
  // 监听自动创建会话的自定义事件
  window.addEventListener("session-created", handleAutoSessionCreated)
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener("session-created", handleAutoSessionCreated)
})
</script>

<template>
  <VNavigationDrawer
    :model-value="true"
    :width="isCollapsed ? 60 : 280"
    class="sidebar"
    :class="{ 'sidebar-collapsed': isCollapsed }"
    permanent>
    <div class="sidebar-content">
      <!-- 品牌标识区域 -->
      <div class="brand-section">
        <!-- 品牌头部容器 - 使用单一容器避免突然切换 -->
        <div class="brand-header-container">
          <!-- 展开状态的品牌头部 -->
          <Transition name="fade-slide" mode="out-in">
            <div v-if="!isCollapsed" key="expanded" class="brand-header">
              <SidebarBrand :is-collapsed="false" @logo-click="handleLogoClick" />
              <SidebarToggle :is-collapsed="false" @toggle="handleToggleSidebar" />
            </div>
            <!-- 收缩状态下的logo和展开按钮 -->
            <div v-else key="collapsed" class="collapsed-header">
              <SidebarToggle :is-collapsed="true" @toggle="handleToggleSidebar" />
              <SidebarBrand :is-collapsed="true" @logo-click="handleLogoClick" />
            </div>
          </Transition>
        </div>

        <!-- 新对话按钮区域 -->
        <SidebarNewChat :is-collapsed="isCollapsed" @session-created="handleSessionCreated" />
      </div>

      <!-- 对话历史 -->
      <ConversationHistory
        ref="conversationHistoryRef"
        v-if="!isCollapsed"
        @conversation-select="handleConversationSelect"
        @conversation-rename="handleConversationRename"
        @conversation-delete="handleConversationDelete" />

      <!-- 底部菜单 -->
      <div class="bottom-menu">
        <!-- 底部菜单容器 - 使用过渡动画 -->
        <Transition name="fade-slide" mode="out-in">
          <!-- 展开状态的底部菜单 -->
          <VList v-if="!isCollapsed" key="expanded-menu" density="compact" class="expanded-menu">
            <VListItem title="移动端 App" @click="openMobileApp">
              <template #prepend>
                <VAvatar size="32" class="app-icon-avatar">
                  <img :src="appIconUrl" alt="App" class="app-icon" />
                </VAvatar>
              </template>
              <template #append>
                <VChip color="success" size="x-small" variant="flat"> 新版 </VChip>
              </template>
            </VListItem>

            <!-- 个人资料菜单 -->
            <UserProfileMenu :is-collapsed="false" @settings-open="handleSettingsOpen" />
          </VList>

          <!-- 收缩状态的底部菜单 -->
          <div v-else key="collapsed-menu" class="collapsed-bottom-menu">
            <VBtn
              variant="text"
              size="default"
              class="collapsed-menu-btn app-icon-btn"
              @click="openMobileApp">
              <img :src="appIconUrl" alt="App" class="app-icon-small" />
            </VBtn>

            <!-- 收缩状态的个人资料菜单 -->
            <UserProfileMenu :is-collapsed="true" @settings-open="handleSettingsOpen" />
          </div>
        </Transition>
      </div>
    </div>
  </VNavigationDrawer>

  <!-- 系统设置弹窗 -->
  <SystemSettingsDialog v-model="settingsDialogOpen" />

  <!-- 移动端App提示 -->
  <VSnackbar
    v-model="showMobileAppTip"
    :timeout="4000"
    color="info"
    location="top"
    class="mobile-app-tip">
    <VIcon icon="mdi-cellphone" class="me-2" />
    Dolphin 助手正在开发当中，敬请期待！
    <template #actions>
      <VBtn icon="mdi-close" size="small" variant="text" @click="showMobileAppTip = false" />
    </template>
  </VSnackbar>
</template>

<style scoped>
.sidebar {
  background-color: var(--app-bg-primary) !important;
  height: 100vh;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.3s ease;
  overflow: hidden; /* 防止整个侧边栏出现滚动条 */
}

/* 侧边栏整体滚动条样式 - 使用全局样式基础上的定制 */
.sidebar ::-webkit-scrollbar {
  width: 6px;
}

.sidebar ::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.08) 0%, rgba(0, 0, 0, 0.15) 100%);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar:hover ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.15) 0%, rgba(0, 0, 0, 0.25) 100%);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.sidebar ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.3) 100%);
  transform: scale(1.1);
}

.sidebar ::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.35) 100%);
  transform: scale(0.95);
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--app-bg-primary);
  transition: background-color 0.3s ease;
}

.brand-section {
  background-color: var(--app-bg-primary);
  padding: 20px 16px;
  transition: background-color 0.3s ease;
}

/* 收缩状态下的品牌区域居中 */
.sidebar-collapsed .brand-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px 8px 8px;
}

/* 品牌头部容器 */
.brand-header-container {
  position: relative;
  min-height: 48px; /* 确保容器有固定高度避免跳动 */
  margin-bottom: 14px;
}

/* 收缩状态下的品牌头部容器 */
.sidebar-collapsed .brand-header-container {
  min-height: auto;
  margin-bottom: -8px;
}

.brand-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.collapsed-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

/* 过渡动画样式 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(-10px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(10px);
}

.fade-slide-enter-to,
.fade-slide-leave-from {
  opacity: 1;
  transform: translateX(0);
}

/* 对话历史占位符 */
.conversation-placeholder {
  flex: 1;
  min-height: 100px;
}

.collapsed-bottom-menu {
  display: flex;
  flex-direction: column;
  padding: 8px 8px 12px 8px;
  gap: 6px;
  align-items: center;
}

/* 展开菜单样式优化 */
.expanded-menu {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none !important;
  box-shadow: none !important;
}

/* 移除VList的默认边框和分隔线 */
.expanded-menu :deep(.v-list) {
  border: none !important;
  box-shadow: none !important;
}

.expanded-menu :deep(.v-list-item) {
  border-top: none !important;
  border-bottom: none !important;
}

.collapsed-menu-btn {
  width: 100%;
  color: rgb(var(--v-theme-on-surface)) !important;
  opacity: 0.8;
}

.collapsed-menu-btn:hover {
  opacity: 1;
  background-color: var(--app-hover-bg);
}

.sidebar-collapsed {
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 侧边栏内容过渡 */
.sidebar-content {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bottom-menu {
  flex-shrink: 0; /* 防止底部菜单被压缩 */
  margin-top: auto;
  background-color: var(--app-bg-primary);
  transition: background-color 0.3s ease;
  border: none !important;
  box-shadow: none !important;
}

/* 移除底部菜单的所有边框和分隔线 */
.bottom-menu :deep(.v-list) {
  border: none !important;
  box-shadow: none !important;
  border-top: none !important;
}

.bottom-menu :deep(.v-list-item) {
  border-top: none !important;
  border-bottom: none !important;
}

.bottom-menu :deep(.v-list-item:first-child) {
  border-top: none !important;
}

/* 底部菜单图标样式 */
.bottom-menu .v-list-item .v-icon {
  color: rgb(var(--v-theme-on-surface)) !important;
  opacity: 0.8;
}

.bottom-menu .v-list-item:hover .v-icon {
  opacity: 1;
}

/* App 图标样式 */
.app-icon-avatar {
  background-color: transparent !important;
}

.app-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  /* filter样式现在由主题CSS统一管理 */
}

.app-icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  min-width: 44px;
  border-radius: 8px;
  padding: 0;
  margin: 0 auto;
}

.app-icon-small {
  width: 22px;
  height: 22px;
  object-fit: contain;
  /* filter样式现在由主题CSS统一管理 */
}

/* 移动端App提示样式 */
.mobile-app-tip {
  z-index: 9999;
}

.mobile-app-tip :deep(.v-snackbar__wrapper) {
  background-color: var(--app-bg-primary) !important;
  color: var(--app-text-primary) !important;
  border: 1px solid var(--app-border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.mobile-app-tip :deep(.v-btn) {
  color: var(--app-text-secondary) !important;
}

/* 移动端样式 */
@media (max-width: 768px) {
  .sidebar {
    /* 保持正常的侧边栏位置，只是宽度会因为收缩而变小 */
    height: 100vh;
  }

  .sidebar-content {
    height: 100%;
  }

  .brand-section {
    padding: 16px 8px;
  }

  .bottom-menu {
    margin-top: auto;
    background-color: var(--app-bg-primary);
  }

  /* 移动端收缩状态下的特殊样式 */
  .sidebar-collapsed .brand-section {
    padding: 12px 6px;
  }
}
</style>
