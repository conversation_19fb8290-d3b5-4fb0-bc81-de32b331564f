<template>
  <div class="input-text-area">
    <textarea
      ref="textareaRef"
      :value="modelValue"
      :placeholder="placeholder"
      class="message-input"
      rows="1"
      @input="handleInput"
      @keydown="handleKeydown"
      @paste="$emit('paste', $event)"
      @dragover="handleDragOver"
      @drop="handleDrop"></textarea>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 定义 props
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '给Dolphin AI 发送消息...',
  },
})

// 定义 emits
const emit = defineEmits(['update:modelValue', 'keydown', 'paste', 'input', 'dragover', 'drop'])

// 响应式数据
const textareaRef = ref(null)

// 处理输入
const handleInput = (event) => {
  emit('update:modelValue', event.target.value)
  emit('input', event)
}

// 处理键盘事件
const handleKeydown = (event) => {
  emit('keydown', event)
}

// 处理拖拽悬停
const handleDragOver = (event) => {
  emit('dragover', event)
}

// 处理拖拽放置
const handleDrop = (event) => {
  emit('drop', event)
}

// 暴露 ref 给父组件
defineExpose({
  textareaRef,
})
</script>

<style scoped>
.input-text-area {
  padding: 12px 20px 6px 20px;
  display: flex;
  align-items: flex-end;
  min-height: 40px;
  position: relative;
}

.message-input {
  width: 100%;
  min-height: 44px;
  max-height: 140px;
  border: none;
  outline: none;
  background: transparent !important;
  background-color: transparent !important;
  font-size: 18px;
  line-height: 1.6;
  color: var(--app-text-primary);
  font-family: 'Microsoft YaHei', '微软雅黑', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
  font-weight: 400;
  padding: 10px 0;
  margin: 0;
  resize: none;
  overflow-y: auto;
  word-wrap: break-word;
  white-space: pre-wrap;
  transition: all 0.3s ease;
}

.message-input::placeholder {
  color: var(--app-text-tertiary);
  font-size: 16px;
  font-weight: 400;
}

/* 美化滚动条 */
.message-input::-webkit-scrollbar {
  width: 4px;
}

.message-input::-webkit-scrollbar-track {
  background: transparent;
}

.message-input::-webkit-scrollbar-thumb {
  background: #d0d0d0;
  border-radius: 2px;
}

.message-input::-webkit-scrollbar-thumb:hover {
  background: #b0b0b0;
}

/* 深色主题下的滚动条 */
[data-theme="dark"] .message-input::-webkit-scrollbar-thumb {
  background: #555555;
}

[data-theme="dark"] .message-input::-webkit-scrollbar-thumb:hover {
  background: #777777;
}

/* 深色主题下确保输入区域背景透明 */
[data-theme="dark"] .input-text-area {
  background: transparent !important;
  background-color: transparent !important;
}

[data-theme="dark"] .message-input {
  background: transparent !important;
  background-color: transparent !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .input-text-area {
    padding: 8px 12px 4px 12px;
  }

  .message-input {
    font-size: 16px; /* 防止iOS缩放 */
  }
}
</style>
