/**
 * router/index.js
 *
 * Vue Router configuration
 */

import { createRouter, createWebHashHistory } from "vue-router"
import baseRoutes from "./base.routes"
import { useAuthStore } from "@/stores/authstore"

const router = createRouter({
  history: createWebHashHistory(),
  routes: [...baseRoutes],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 公开路由（不需要认证的路由）
  const publicRoutes = ['login']
  const isPublicRoute = publicRoutes.includes(to.name)

  // 如果是公开路由，直接放行
  if (isPublicRoute) {
    // 如果已登录用户访问登录页面，重定向到首页
    if (to.name === 'login' && authStore.isLoggedIn) {
      next({ name: 'home' })
      return
    }
    next()
    return
  }

  // 检查路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

  if (requiresAuth || to.path === '/') {
    // 需要认证的路由，先初始化认证状态
    await authStore.initAuth()

    // 检查登录状态
    if (!authStore.isLoggedIn) {
      console.warn('🔒 用户未登录，重定向到登录页')
      next({ name: 'login' })
      return
    }
  }

  // 正常访问
  next()
})

export default router
