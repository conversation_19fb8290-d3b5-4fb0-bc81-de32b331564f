<script setup>
import { ref, onMounted } from "vue"
import { AiSettingService } from "@/api"

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const aiConfig = ref({
  maxTokens: 16384,
  normalPrompt: "你是一个有用的AI助手。",
  deepThinkPrompt: "你是一个具有深度思考能力的AI助手，请仔细分析问题并给出详细的回答。",
})

// 消息提示相关
const showSuccessMessage = ref(false)
const showErrorMessage = ref(false)
const successMessage = ref("")
const errorMessage = ref("")

// 获取当前AI设置
const fetchAiSettings = async () => {
  loading.value = true
  try {
    // 分别获取token和两种提示词设置
    const [tokenResult, normalPromptResult, deepThinkPromptResult] = await Promise.all([
      AiSettingService.getTokenSetting(),
      AiSettingService.getPromptSetting(false), // 普通提示词
      AiSettingService.getPromptSetting(true), // 深度思考提示词
    ])


    // 更新token设置
    if (tokenResult.success && tokenResult.data) {
      const tokenValue = tokenResult.data
      const tokenNumber = parseInt(tokenValue)
      if (!isNaN(tokenNumber)) {
        aiConfig.value.maxTokens = tokenNumber
        console.log("✅ Token设置已更新:", tokenNumber)
      } else {
        console.warn("⚠️ Token值格式错误:", tokenValue)
      }
    } else {
      console.error("❌ 获取Token设置失败:", tokenResult.message)
    }

    // 更新普通提示词设置
    if (normalPromptResult.success && normalPromptResult.data) {
      console.log("✅ 普通提示词设置获取成功:", normalPromptResult.data)
      if (typeof normalPromptResult.data === "string") {
        aiConfig.value.normalPrompt = normalPromptResult.data
        console.log("✅ 普通提示词内容已更新:", normalPromptResult.data.substring(0, 50) + "...")
      } else if (typeof normalPromptResult.data === "object" && normalPromptResult.data.prompt) {
        aiConfig.value.normalPrompt = normalPromptResult.data.prompt
     
       
      }
    } else {
      console.error("❌ 获取普通提示词设置失败:", normalPromptResult.message)
    }

    // 更新深度思考提示词设置
    if (deepThinkPromptResult.success && deepThinkPromptResult.data) {
      console.log("✅ 深度思考提示词设置获取成功:", deepThinkPromptResult.data)
      if (typeof deepThinkPromptResult.data === "string") {
        aiConfig.value.deepThinkPrompt = deepThinkPromptResult.data
        console.log(
          "✅ 深度思考提示词内容已更新:",
          deepThinkPromptResult.data.substring(0, 50) + "..."
        )
      } else if (
        typeof deepThinkPromptResult.data === "object" &&
        deepThinkPromptResult.data.prompt
      ) {
        aiConfig.value.deepThinkPrompt = deepThinkPromptResult.data.prompt
        console.log(
          "✅ 深度思考提示词内容已更新:",
          deepThinkPromptResult.data.prompt.substring(0, 50) + "..."
        )
      }
    } else {
      console.error("❌ 获取深度思考提示词设置失败:", deepThinkPromptResult.message)
    }

    console.log("🔧 最终AI配置:", aiConfig.value)
  } catch (error) {
    console.error("💥 获取AI设置时发生错误:", error)
  } finally {
    loading.value = false
  }
}

// 显示成功消息
const showSuccess = (message) => {
  successMessage.value = message
  showSuccessMessage.value = true
}

// 显示错误消息
const showError = (message) => {
  errorMessage.value = message
  showErrorMessage.value = true
}

// 隐藏成功消息
const hideSuccessMessage = () => {
  showSuccessMessage.value = false
}

// 隐藏错误消息
const hideErrorMessage = () => {
  showErrorMessage.value = false
}

// 保存AI设置
const saveAiSettings = async () => {
  saving.value = true

  try {
    // 分别保存token设置和两种提示词设置
    const [tokenResult, normalPromptResult, deepThinkPromptResult] = await Promise.all([
      AiSettingService.setTokenSetting(aiConfig.value.maxTokens),
      AiSettingService.setPromptSetting(aiConfig.value.normalPrompt, false), // 普通提示词
      AiSettingService.setPromptSetting(aiConfig.value.deepThinkPrompt, true), // 深度思考提示词
    ])

    // 检查保存结果
    const results = [tokenResult, normalPromptResult, deepThinkPromptResult]
    const successCount = results.filter((r) => r.success).length

    if (successCount === results.length) {
      console.log("✅ 所有AI设置保存成功")
      showSuccess("所有设置保存成功！")
    } else {
      const errors = []
      if (!tokenResult.success) errors.push("Token设置保存失败")
      if (!normalPromptResult.success) errors.push("普通提示词保存失败")
      if (!deepThinkPromptResult.success) errors.push("深度思考提示词保存失败")

      const errorMsg = errors.join("、")
      console.error("❌ 部分AI设置保存失败:", errorMsg)
      showError(`部分设置保存失败：${errorMsg}`)
    }
  } catch (error) {
    console.error("💥 保存AI设置时发生错误:", error)
    showError("保存设置时发生错误，请稍后重试")
  } finally {
    saving.value = false
  }
}

// 组件挂载时获取设置
onMounted(() => {
  fetchAiSettings()
})
</script>

<template>
  <div class="ai-settings">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <VProgressCircular indeterminate color="primary" size="24" />
      <span class="loading-text">加载AI设置中...</span>
    </div>

    <!-- AI设置表单 -->
    <div v-else>
      <!-- Token限制设置 -->
      <div class="setting-row">
        <div class="setting-info">
          <span class="setting-label">Token限制</span>
          <span class="setting-description">当前: {{ aiConfig.maxTokens }}</span>
        </div>
        <div class="token-controls">
          <VTextField
            v-model.number="aiConfig.maxTokens"
            type="number"
            variant="outlined"
            density="compact"
            class="token-input"
            hide-details
            :min="1000"
            :max="32000" />
        </div>
      </div>

      <!-- Token滑块 -->
      <div class="setting-row">
        <div class="slider-container">
          <VSlider
            v-model="aiConfig.maxTokens"
            :min="1000"
            :max="32000"
            :step="100"
            color="primary"
            class="token-slider"
            hide-details />
          <div class="slider-labels">
            <span class="slider-label">1000</span>
            <span class="slider-label">32000</span>
          </div>
        </div>
      </div>

      <!-- 普通提示词设置 -->
      <div class="setting-row">
        <div class="setting-info">
          <span class="setting-label">普通提示词</span>
          <span class="setting-description">普通对话模式下AI助手的行为和回复风格</span>
        </div>
      </div>

      <div class="setting-row">
        <VTextarea
          v-model="aiConfig.normalPrompt"
          variant="outlined"
          density="compact"
          class="prompt-textarea"
          hide-details
          rows="4"
          placeholder="输入普通提示词..." />
      </div>

      <!-- 深度思考提示词设置 -->
      <div class="setting-row">
        <div class="setting-info">
          <span class="setting-label">深度思考提示词</span>
          <span class="setting-description">深度思考模式下AI助手的行为和回复风格</span>
        </div>
      </div>

      <div class="setting-row">
        <VTextarea
          v-model="aiConfig.deepThinkPrompt"
          variant="outlined"
          density="compact"
          class="prompt-textarea"
          hide-details
          rows="4"
          placeholder="输入深度思考提示词..." />
      </div>

      <!-- 保存按钮 -->
      <div class="setting-row">
        <VSpacer />
        <VBtn
          variant="flat"
          color="primary"
          @click="saveAiSettings"
          :loading="saving"
          :disabled="saving"
          class="save-btn">
          保存设置
        </VBtn>
      </div>
    </div>

    <!-- 成功提示 Snackbar -->
    <VSnackbar
      v-model="showSuccessMessage"
      :timeout="3000"
      color="success"
      location="top"
      @click="hideSuccessMessage">
      <VIcon icon="mdi-check-circle" class="me-2" />
      {{ successMessage }}
      <template #actions>
        <VBtn icon="mdi-close" size="small" variant="text" @click="hideSuccessMessage" />
      </template>
    </VSnackbar>

    <!-- 错误提示 Snackbar -->
    <VSnackbar
      v-model="showErrorMessage"
      :timeout="5000"
      color="error"
      location="top"
      @click="hideErrorMessage">
      <VIcon icon="mdi-alert-circle" class="me-2" />
      {{ errorMessage }}
      <template #actions>
        <VBtn icon="mdi-close" size="small" variant="text" @click="hideErrorMessage" />
      </template>
    </VSnackbar>
  </div>
</template>

<style scoped>
.ai-settings {
  max-width: 100%;
}

.loading-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 40px 20px;
  justify-content: center;
}

.loading-text {
  font-size: 14px;
  color: var(--app-text-secondary);
  transition: color 0.3s ease;
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 0;
  border-bottom: 1px solid var(--app-border-light);
  transition: border-color 0.3s ease;
}

.setting-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.setting-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.setting-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--app-text-primary);
  transition: color 0.3s ease;
}

.setting-description {
  font-size: 12px;
  color: var(--app-text-secondary);
  font-weight: 400;
  transition: color 0.3s ease;
}

.token-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.token-input {
  width: 120px;
  flex-shrink: 0;
}

.slider-container {
  width: 100%;
  padding: 0 8px;
}

.token-slider {
  margin-bottom: 8px;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  padding: 0 4px;
}

.slider-label {
  font-size: 12px;
  color: var(--app-text-secondary);
}

.prompt-textarea {
  width: 100%;
}

.save-btn {
  margin-left: auto;
}

/* 深色主题适配 */
[data-theme="dark"] .ai-settings {
  color: var(--app-text-primary);
}

[data-theme="dark"] .setting-row {
  border-color: var(--app-border-color);
}
</style>
