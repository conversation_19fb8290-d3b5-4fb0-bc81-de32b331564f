<template>
  <VTooltip text="上传附件（仅识别文字）最多4张，每张10MB，支持各类图片格式" location="top">
    <template #activator="{ props }">
      <div
        v-bind="props"
        class="image-upload-area"
        @click="$emit('upload-click')"
        @dragover="$emit('drag-over', $event)"
        @drop="$emit('drop', $event)">
    <!-- 有图片时显示图片预览 -->
    <div v-if="attachedImages.length > 0" class="uploaded-images">
      <div
        v-for="image in attachedImages"
        :key="image.id"
        class="uploaded-image-item"
        @click.stop="$emit('preview-image', image)">
        <img :src="image.url" :alt="image.name" class="uploaded-image-preview" />

        <!-- 上传进度遮罩 -->
        <div v-if="image.uploading" class="upload-progress-overlay">
          <div class="progress-content">
            <VProgressCircular
              :model-value="image.progress || 0"
              :size="40"
              :width="4"
              color="white"
            >
              {{ image.progress || 0 }}%
            </VProgressCircular>
            <div class="progress-text">上传中...</div>
          </div>
        </div>

        <div class="uploaded-image-overlay" :class="{ 'uploading': image.uploading }">
          <span class="uploaded-image-name">{{ image.name }}</span>
          <div class="overlay-actions" v-if="!image.uploading">
            <VBtn
              icon="mdi-eye-outline"
              size="small"
              variant="text"
              color="white"
              @click.stop="$emit('preview-image', image)" />
            <VBtn
              icon="mdi-close"
              size="small"
              variant="text"
              color="white"
              @click.stop="$emit('remove-image', image.id)" />
          </div>
        </div>
      </div>
      <!-- 添加更多图片的按钮 -->
      <div
        class="add-more-images"
        @click.stop="$emit('upload-click')"
        v-if="!isUploading && attachedImages.length < 4">
        <VIcon icon="mdi-plus" size="32" color="grey-lighten-1" />
        <span class="add-more-text">添加更多</span>
      </div>
    </div>

    <!-- 没有图片时显示上传提示 -->
    <div v-else class="upload-placeholder">
      <div v-if="isUploading" class="uploading-placeholder">
        <VProgressCircular
          :model-value="uploadProgress"
          :size="48"
          :width="4"
          color="primary"
        >
          {{ uploadProgress }}%
        </VProgressCircular>
        <div class="uploading-text">上传中...</div>
      </div>
      <img v-else :src="photosAndFilesIconUrl" alt="上传附件" class="upload-icon" />
    </div>
      </div>
    </template>
  </VTooltip>
</template>

<script setup>
// 导入SVG图标
import photosAndFilesIconUrl from "@/assets/icons/photos and files.svg"

// 定义 props
defineProps({
  attachedImages: {
    type: Array,
    default: () => [],
  },
  isUploading: {
    type: Boolean,
    default: false,
  },
  uploadProgress: {
    type: Number,
    default: 0,
  },
})

// 定义 emits
defineEmits([
  'upload-click',
  'drag-over',
  'drop',
  'preview-image',
  'remove-image',
])
</script>

<style scoped>
/* 图片上传区域样式 */
.image-upload-area {
  min-height: 90px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  position: relative;
  background: transparent !important;
}

.image-upload-area:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%) !important;
}

/* 深色主题下的图片上传区域 */
[data-theme="dark"] .image-upload-area {
  background: transparent !important;
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .image-upload-area:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.02) 100%) !important;
}

/* 上传占位符样式 */
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60px;
  text-align: center;
  border-radius: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: transparent;
}

.image-upload-area:hover .upload-placeholder {
  background: rgba(102, 126, 234, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.1);
}

/* 上传中占位符样式 */
.uploading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.uploading-text {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

/* 上传图标样式 */
.upload-icon {
  width: 48px;
  height: 48px;
  opacity: 0.6;
  transition: all 0.3s ease;
  filter: brightness(0) saturate(100%) invert(60%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(90%) contrast(90%);
}

.image-upload-area:hover .upload-icon {
  opacity: 0.8;
  transform: scale(1.1);
}

/* 已上传图片样式 */
.uploaded-images {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  align-items: center;
}

.uploaded-image-item {
  position: relative;
  width: 110px;
  height: 110px;
  border-radius: 20px;
  overflow: hidden;
  border: 3px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.9);
}

.uploaded-image-item:hover {
  border-color: rgba(102, 126, 234, 0.5);
  transform: scale(1.05) translateY(-4px);
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
}

.uploaded-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.uploaded-image-item:hover .uploaded-image-preview {
  transform: scale(1.1);
}

/* 上传进度遮罩 */
.upload-progress-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(2px);
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.progress-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.uploaded-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 10px;
  opacity: 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.uploaded-image-overlay.uploading {
  opacity: 0;
}

.uploaded-image-item:hover .uploaded-image-overlay:not(.uploading) {
  opacity: 1;
}

.uploaded-image-name {
  color: white;
  font-size: 11px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.5px;
}

.overlay-actions {
  display: flex;
  gap: 6px;
  align-self: flex-end;
}

.uploaded-image-overlay .v-btn {
  min-width: 28px;
  width: 28px;
  height: 28px;
  background: rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.uploaded-image-overlay .v-btn:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scale(1.1);
}

/* 添加更多图片按钮 */
.add-more-images {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  border: 2px dashed #d0d0d0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-more-images:hover {
  border-color: #1976d2;
  background-color: rgba(25, 118, 210, 0.05);
}

.add-more-text {
  margin-top: 4px;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-upload-area {
    min-height: 70px;
    padding: 8px;
  }

  .upload-placeholder {
    min-height: 50px;
  }

  .uploaded-image-item {
    width: 80px;
    height: 80px;
  }

  .add-more-images {
    width: 80px;
    height: 80px;
  }

  .uploaded-images {
    gap: 8px;
  }
}
</style>
