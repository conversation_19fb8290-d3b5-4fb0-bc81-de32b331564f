<template>
  <VDialog
    v-model="dialogVisible"
    persistent
    no-click-animation
    width="320"
    class="global-loading-dialog">
    <VCard class="loading-card">
      <VCardText class="loading-content">
        <!-- 加载动画 -->
        <div class="loading-animation">
          <div class="dolphin-loading">
            <!-- 海豚Logo图片 -->
            <div class="dolphin-logo">
              <img src="@/assets/logo.webp" alt="Dolphin AI" class="dolphin-image" />
            </div>
            <!-- 水波纹效果 -->
            <div class="water-waves">
              <div class="wave wave-1"></div>
              <div class="wave wave-2"></div>
              <div class="wave wave-3"></div>
            </div>
          </div>
        </div>

        <!-- 加载文本 -->
        <div class="loading-text">
          <div class="main-text">{{ loadingText }}</div>
          <div class="sub-text">{{ subText }}</div>
        </div>

        <!-- 进度指示器（可选） -->
        <div v-if="showProgress" class="progress-container">
          <VProgressLinear
            :model-value="progress"
            color="primary"
            height="4"
            rounded
            class="progress-bar" />
          <div class="progress-text">{{ progressText }}</div>
        </div>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<script setup>
import { ref, computed, watch } from "vue"

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  loadingText: {
    type: String,
    default: "正在加载对话数据",
  },
  subText: {
    type: String,
    default: "请稍候...",
  },
  showProgress: {
    type: Boolean,
    default: false,
  },
  progress: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0 && value <= 100,
  },
  progressText: {
    type: String,
    default: "",
  },
})

// Emits
const emit = defineEmits(["update:modelValue"])

// 内部状态
const dialogVisible = ref(props.modelValue)

// 监听外部 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    dialogVisible.value = newVal
  }
)

// 监听内部状态变化，同步到外部
watch(dialogVisible, (newVal) => {
  emit("update:modelValue", newVal)
})
</script>

<style scoped>
.global-loading-dialog :deep(.v-overlay__content) {
  margin: 0;
}

.loading-card {
  background: var(--app-bg-primary);
  border: 1px solid var(--app-border-color);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 24px;
  text-align: center;
}

/* 加载动画 */
.loading-animation {
  margin-bottom: 24px;
}

.dolphin-loading {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 80px;
}

.dolphin-logo {
  position: relative;
  animation: dolphinSwim 2s ease-in-out infinite;
  transform-origin: center center;
  margin-bottom: 10px;
}

.dolphin-image {
  width: 60px;
  height: 60px;
  object-fit: contain;
  filter: drop-shadow(0 4px 12px rgba(79, 195, 247, 0.3));
  transition: filter 0.3s ease;
}

/* 水波纹效果 */
.water-waves {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 20px;
}

.wave {
  position: absolute;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #4fc3f7, transparent);
  border-radius: 1px;
  animation: waveFlow 2s ease-in-out infinite;
}

.wave-1 {
  bottom: 0;
  animation-delay: 0s;
}

.wave-2 {
  bottom: 4px;
  animation-delay: 0.3s;
  opacity: 0.7;
}

.wave-3 {
  bottom: 8px;
  animation-delay: 0.6s;
  opacity: 0.4;
}

/* 海豚游泳动画 */
@keyframes dolphinSwim {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-3px) rotate(-2deg);
  }
  50% {
    transform: translateY(-5px) rotate(0deg);
  }
  75% {
    transform: translateY(-3px) rotate(2deg);
  }
}

/* 水波流动动画 */
@keyframes waveFlow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 加载文本 */
.loading-text {
  margin-bottom: 20px;
}

.main-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--app-text-primary);
  margin-bottom: 8px;
  font-family: "Microsoft YaHei", sans-serif;
}

.sub-text {
  font-size: 14px;
  color: var(--app-text-secondary);
  opacity: 0.8;
  font-family: "Microsoft YaHei", sans-serif;
}

/* 进度指示器 */
.progress-container {
  width: 100%;
  margin-top: 16px;
}

.progress-bar {
  margin-bottom: 8px;
}

.progress-text {
  font-size: 12px;
  color: var(--app-text-secondary);
  text-align: center;
  font-family: "Microsoft YaHei", sans-serif;
}

/* 深色主题适配 */
.theme--dark .loading-card {
  background: var(--app-bg-primary);
  border-color: var(--app-border-color);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .loading-content {
    padding: 24px 20px;
  }

  .dolphin-loading {
    width: 80px;
    height: 60px;
  }

  .dolphin-image {
    width: 50px;
    height: 50px;
  }

  .water-waves {
    width: 60px;
    height: 16px;
  }

  .main-text {
    font-size: 15px;
  }

  .sub-text {
    font-size: 13px;
  }
}
</style>
