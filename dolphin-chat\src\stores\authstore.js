import { defineStore } from "pinia"
import { ref } from "vue"
import { AuthService } from "@/api/auth"
import tokenManager from "@/utils/tokenManager"

export const useAuthStore = defineStore("auth", () => {
  // 状态
  const isLoggedIn = ref(false)
  const user = ref(null)
  const loading = ref(false)
  const error = ref("")
  const tokenCheckInterval = ref(null) // 定时检查token的定时器

  // 登录方法
  const login = async (credentials) => {
    loading.value = true
    error.value = ""

    try {
      // 调用真实 API
      const result = await AuthService.login(credentials)

      if (result.success) {
        // 设置用户信息
        user.value = result.data.user
        isLoggedIn.value = true

        // 保存到 localStorage (AuthService 已经处理了 token 保存)
        localStorage.setItem("user", JSON.stringify(user.value))
        localStorage.setItem("isLoggedIn", "true")

        // 启动token定时检查
        startTokenCheck()

        return { success: true }
      } else {
        throw new Error(result.message || "登录失败")
      }
    } catch (err) {
      error.value = err.message || "登录失败，请检查网络连接"
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  // 登出方法
  const logout = () => {
    // 停止token定时检查
    stopTokenCheck()

    // 直接清除本地状态，不调用API
    user.value = null
    isLoggedIn.value = false
    error.value = ""

    // 清除所有 localStorage 信息
    localStorage.clear()

    // 也清除 sessionStorage（如果需要的话）
    // sessionStorage.clear()
  }

  // 初始化认证状态
  const initAuth = async () => {
    const savedUser = localStorage.getItem("user")
    const savedLoginStatus = localStorage.getItem("isLoggedIn")
    const token = localStorage.getItem("token")

    // 检查是否有完整的认证信息
    if (savedUser && savedLoginStatus === "true" && token) {
      try {
        // 首先进行本地token格式检查
        if (!AuthService.isTokenValid()) {
          console.warn('🔒 本地token已过期，清除认证状态')
          logout()
          return
        }

        // 进行服务器端token验证
        console.log('🔍 正在验证token有效性...')
        const validationResult = await AuthService.validateTokenWithServer()

        if (validationResult.success && validationResult.valid) {
          // 服务器验证成功，设置登录状态
          console.log('✅ Token验证成功，用户已登录')
          user.value = JSON.parse(savedUser)
          isLoggedIn.value = true
          // 启动token定时检查
          startTokenCheck()
        } else {
          // 服务器验证失败，检查状态码
          if (validationResult.code >= 4200 && validationResult.code < 4300) {
            console.warn(`🔒 服务器验证失败 (code: ${validationResult.code})，清除认证状态`)
          } else {
            console.warn('🔒 Token验证失败，清除认证状态')
          }
          logout()
        }
      } catch (err) {
        console.error('🔒 认证状态初始化失败:', err)
        // 清除无效数据
        logout()
      }
    } else {
     
      logout()
    }
  }

  // 清除错误
  const clearError = () => {
    error.value = ""
  }

  // 更新用户信息
  const updateUser = (userData) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      // 更新 localStorage
      localStorage.setItem("user", JSON.stringify(user.value))
    }
  }

  // 启动token定时检查
  const startTokenCheck = () => {
    tokenManager.startTokenCheck()
  }

  // 停止token定时检查
  const stopTokenCheck = () => {
    tokenManager.stopTokenCheck()
  }

  // 处理token过期
  const handleTokenExpired = () => {
    tokenManager.handleTokenExpired()
    // 清除本地认证状态
    user.value = null
    isLoggedIn.value = false
    error.value = ""
  }

  // 手动检查token有效性
  const checkTokenValidity = () => {
    if (!isLoggedIn.value) return false
    return tokenManager.checkTokenValidity()
  }

  return {
    // 状态
    isLoggedIn,
    user,
    loading,
    error,

    // 方法
    login,
    logout,
    initAuth,
    clearError,
    updateUser,
    startTokenCheck,
    stopTokenCheck,
    handleTokenExpired,
    checkTokenValidity,
  }
})
