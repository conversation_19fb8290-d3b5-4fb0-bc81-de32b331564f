/**
 * 消息相关API服务
 */

import request from '@/utils/request'
import { MESSAGE_API } from './constants'
import ResourceService from './resource'

/**
 * 消息API服务类
 */
export class MessageService {
  /**
   * 获取消息列表
   * @param {string|number} sessionId 会话ID
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @returns {Promise} 消息列表
   */
  static async getMessageList(sessionId, params = {}) {
    try {


      const response = await request.get(MESSAGE_API.GET_LIST,
        {
          params: {
            sessionId: sessionId
          }
        }
      )


      // 检查响应格式
      if (response.code === 200 && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.msg || '获取消息列表成功'
        }
      } else {
        throw new Error(response.msg || '获取消息列表失败')
      }
    } catch (error) {
      console.error('获取消息列表错误:', error)
      return {
        success: false,
        error: error.message,
        message: '获取消息列表失败',
        data: []
      }
    }
  }

  /**
   * 删除会话中的所有消息（清空会话）
   * @param {string|number} sessionId 会话ID
   * @returns {Promise} 删除结果
   */
  static async deleteSessionMessages(sessionId) {
    try {
      // 将sessionId作为Query参数拼接到URL中
      const url = `${MESSAGE_API.DELETE}?sessionId=${sessionId}`
      const response = await request.delete(url)

      return {
        success: true,
        data: response,
        message: '清空会话消息成功'
      }
    } catch (error) {
      console.error('清空会话消息错误:', error)
      return {
        success: false,
        error: error.message,
        message: '清空会话消息失败'
      }
    }
  }

  /**
   * 格式化消息数据用于前端渲染
   * @param {Array} messages 原始消息数据
   * @param {string} sessionId 会话ID，用于设置消息的conversationId
   * @returns {Array} 格式化后的消息数据
   */
  static formatMessagesForRender(messages, sessionId = null) {
    if (!Array.isArray(messages)) {
      return []
    }

    return messages.map(message => {
      // 将API返回的role字段映射为前端的type字段
      let messageType = 'text'
      if (message.role) {
        switch (message.role.toLowerCase()) {
          case 'user':
            messageType = 'user'
            break
          case 'assistant':
          case 'ai':
            messageType = 'ai'
            break
          default:
            messageType = 'text'
        }
      } else if (message.type) {
        messageType = message.type
      }

      // 基础消息格式
      const formattedMessage = {
        id: message.id || Date.now() + Math.random(),
        type: messageType,
        content: message.content || '',
        timestamp: message.createTime ? new Date(message.createTime) : (message.timestamp ? new Date(message.timestamp) : new Date()),
        conversationId: sessionId || message.sessionId || message.conversationId
      }

      // 处理图片数据 - 从API返回的images字段
      if (message.images && Array.isArray(message.images) && message.images.length > 0) {
        // 过滤掉空值和无效路径
        const validImages = message.images.filter(img => img && typeof img === 'string' && img.trim())

        if (validImages.length > 0) {
          formattedMessage.images = validImages
          formattedMessage.hasImages = true

          // 如果只有一张图片，也设置imageUrl字段（兼容旧代码）
          if (validImages.length === 1) {
            formattedMessage.imageUrl = validImages[0]
          }
        }
      }

      // 根据消息类型进行特殊处理
      switch (message.type) {
        case 'image':
          formattedMessage.imageUrl = message.imageUrl || formattedMessage.imageUrl
          formattedMessage.imageName = message.imageName
          break
        case 'multi-image':
          formattedMessage.images = message.images || formattedMessage.images || []
          break
        case 'ai':
          // AI消息可能包含特殊格式
          formattedMessage.isStreaming = false
          break
        default:
          // 默认文本消息
          break
      }

      return formattedMessage
    })
  }

  /**
   * 获取消息中图片的完整URL
   * @param {string} imagePath 图片路径，如 'chat/image/1944638070133493760.png'
   * @returns {Promise} 图片URL数据
   */
  static async getMessageImageUrl(imagePath) {
    try {
      const result = await ResourceService.getChatImage(imagePath)

      if (result.success) {
        return {
          success: true,
          url: result.data.url, // blob URL
          path: imagePath,
          blob: result.data.blob
        }
      } else {
        throw new Error(result.error || '获取图片失败')
      }
    } catch (error) {
      console.error('获取消息图片URL错误:', error)
      return {
        success: false,
        error: error.message,
        path: imagePath
      }
    }
  }

  /**
   * 批量获取消息中图片的URL
   * @param {Array<string>} imagePaths 图片路径数组
   * @returns {Promise<Array>} 图片URL数据数组
   */
  static async getMessageImageUrls(imagePaths) {
    if (!Array.isArray(imagePaths) || imagePaths.length === 0) {
      return []
    }

    try {
      const promises = imagePaths.map(path => this.getMessageImageUrl(path))
      const results = await Promise.allSettled(promises)

      return results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value
        } else {
          console.error(`获取图片失败 [${imagePaths[index]}]:`, result.reason)
          return {
            success: false,
            error: result.reason?.message || '获取图片失败',
            path: imagePaths[index]
          }
        }
      })
    } catch (error) {
      console.error('批量获取消息图片URL错误:', error)
      return imagePaths.map(path => ({
        success: false,
        error: error.message,
        path: path
      }))
    }
  }

  /**
   * 为消息数据预加载图片URL
   * @param {Array} messages 格式化后的消息数据
   * @returns {Promise<Array>} 包含图片URL的消息数据
   */
  static async preloadMessageImages(messages) {
    if (!Array.isArray(messages)) {
      return []
    }

    const processedMessages = await Promise.all(
      messages.map(async (message) => {
        if (message.images && message.images.length > 0) {
          // 获取所有图片的URL
          const imageResults = await this.getMessageImageUrls(message.images)

          // 将成功获取的URL添加到消息中
          const imageUrls = imageResults
            .filter(result => result.success)
            .map(result => ({
              path: result.path,
              url: result.url,
              blob: result.blob
            }))

          return {
            ...message,
            imageUrls: imageUrls,
            imageLoadErrors: imageResults
              .filter(result => !result.success)
              .map(result => result.path)
          }
        }

        return message
      })
    )

    return processedMessages
  }
}
