/**
 * 全局字体样式配置
 * 统一管理应用的字体设置
 */

/* ===== 字体导入 ===== */
/* Google Fonts - Roboto */
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

/* 可选：其他字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
/* @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100;300;400;500;700;900&display=swap'); */

/* ===== 字体变量定义 ===== */
:root {
  /* 主要字体族 */
  --font-family-sans: 'Microsoft YaHei', '微软雅黑', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  --font-family-serif: 'Microsoft YaHei', '微软雅黑', Georgia, 'Times New Roman', Times, serif;
  --font-family-mono: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace;

  /* 中文字体支持 */
  --font-family-chinese: 'Microsoft YaHei', '微软雅黑', 'PingFang SC', 'Hiragino Sans GB', 'Roboto', 'Noto Sans SC', sans-serif;
  
  /* 字体大小 */
  --font-size-xs: 0.875rem;   /* 14px */
  --font-size-sm: 1rem;       /* 16px */
  --font-size-base: 1.125rem; /* 18px */
  --font-size-lg: 1.25rem;    /* 20px */
  --font-size-xl: 1.375rem;   /* 22px */
  --font-size-2xl: 1.625rem;  /* 26px */
  --font-size-3xl: 2rem;      /* 32px */
  --font-size-4xl: 2.5rem;    /* 40px */
  --font-size-5xl: 3.25rem;   /* 52px */
  
  /* 字体权重 */
  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  
  /* 行高 */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* 字母间距 */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
}

/* ===== 全局字体应用 ===== */
* {
  font-family: var(--font-family-chinese);
}

html {
  font-size: 18px; /* 基础字体大小 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-family-chinese);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
}

/* ===== 标题字体样式 ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-chinese);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin: 0;
}

h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
}

h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
}

h5 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

h6 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* ===== 段落和文本样式 ===== */
p {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  margin: 0;
}

/* ===== 代码字体样式 ===== */
code, pre, kbd, samp {
  font-family: var(--font-family-mono);
}

code {
  font-size: 0.875em;
  font-weight: var(--font-weight-medium);
}

pre {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

/* ===== 输入框字体样式 ===== */
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* ===== 响应式字体大小 ===== */
@media (max-width: 768px) {
  html {
    font-size: 16px; /* 移动端基础字体大小 */
  }

  h1 {
    font-size: var(--font-size-2xl);
  }

  h2 {
    font-size: var(--font-size-xl);
  }

  h3 {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 480px) {
  html {
    font-size: 15px; /* 小屏幕基础字体大小 */
  }
}

/* ===== 字体工具类 ===== */
/* 字体族工具类 */
.font-sans {
  font-family: var(--font-family-sans) !important;
}

.font-serif {
  font-family: var(--font-family-serif) !important;
}

.font-mono {
  font-family: var(--font-family-mono) !important;
}

.font-chinese {
  font-family: var(--font-family-chinese) !important;
}

/* 字体大小工具类 */
.text-xs {
  font-size: var(--font-size-xs) !important;
}

.text-sm {
  font-size: var(--font-size-sm) !important;
}

.text-base {
  font-size: var(--font-size-base) !important;
}

.text-lg {
  font-size: var(--font-size-lg) !important;
}

.text-xl {
  font-size: var(--font-size-xl) !important;
}

.text-2xl {
  font-size: var(--font-size-2xl) !important;
}

.text-3xl {
  font-size: var(--font-size-3xl) !important;
}

.text-4xl {
  font-size: var(--font-size-4xl) !important;
}

.text-5xl {
  font-size: var(--font-size-5xl) !important;
}

/* 字体权重工具类 */
.font-thin {
  font-weight: var(--font-weight-thin) !important;
}

.font-light {
  font-weight: var(--font-weight-light) !important;
}

.font-normal {
  font-weight: var(--font-weight-normal) !important;
}

.font-medium {
  font-weight: var(--font-weight-medium) !important;
}

.font-semibold {
  font-weight: var(--font-weight-semibold) !important;
}

.font-bold {
  font-weight: var(--font-weight-bold) !important;
}

.font-extrabold {
  font-weight: var(--font-weight-extrabold) !important;
}

.font-black {
  font-weight: var(--font-weight-black) !important;
}

/* 行高工具类 */
.leading-none {
  line-height: var(--line-height-none) !important;
}

.leading-tight {
  line-height: var(--line-height-tight) !important;
}

.leading-snug {
  line-height: var(--line-height-snug) !important;
}

.leading-normal {
  line-height: var(--line-height-normal) !important;
}

.leading-relaxed {
  line-height: var(--line-height-relaxed) !important;
}

.leading-loose {
  line-height: var(--line-height-loose) !important;
}

/* 字母间距工具类 */
.tracking-tighter {
  letter-spacing: var(--letter-spacing-tighter) !important;
}

.tracking-tight {
  letter-spacing: var(--letter-spacing-tight) !important;
}

.tracking-normal {
  letter-spacing: var(--letter-spacing-normal) !important;
}

.tracking-wide {
  letter-spacing: var(--letter-spacing-wide) !important;
}

.tracking-wider {
  letter-spacing: var(--letter-spacing-wider) !important;
}

.tracking-widest {
  letter-spacing: var(--letter-spacing-widest) !important;
}
