<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue"
import ModelSelector from "./ModelSelector.vue"
import { useChatStore } from "@/stores/baseStore"

// 定义 props
const props = defineProps({
  sidebarCollapsed: {
    type: Boolean,
    default: false,
  },
  showImageCanvas: {
    type: Boolean,
    default: false,
  },
})

// 定义 emits
const emit = defineEmits([
  "toggle-chat-menu",
  "share-conversation",
  "toggle-photo-wall",
  "clear-conversation",
])

// 使用聊天store
const chatStore = useChatStore()

// 响应式数据
const topBarRef = ref(null)
const screenWidth = ref(window.innerWidth)
const showChatMenu = ref(false)
const menuTrigger = ref(null)

// 计算属性
const isSmallScreen = computed(() => screenWidth.value <= 768)
const isMediumScreen = computed(() => screenWidth.value <= 1024 && screenWidth.value > 768)
const isLargeScreen = computed(() => screenWidth.value > 1024)
const comparisonMode = computed(() => chatStore.comparisonMode)

// 模型选择器的 props
const modelSelectorProps = computed(() => {
  if (screenWidth.value <= 480) {
    return { compact: true, showFullName: false }
  } else if (screenWidth.value <= 768) {
    return { compact: false, showFullName: false }
  } else {
    return { compact: false, showFullName: true }
  }
})

// 方法
const handleResize = () => {
  screenWidth.value = window.innerWidth
}

const toggleChatMenu = () => {
  showChatMenu.value = !showChatMenu.value
  emit("toggle-chat-menu")
}

const handleShareConversation = () => {
  showChatMenu.value = false
  emit("share-conversation")
}

const handleTogglePhotoWall = () => {
  showChatMenu.value = false
  emit("toggle-photo-wall")
}

const handleClearConversation = () => {
  showChatMenu.value = false
  emit("clear-conversation")
}

// 点击外部关闭菜单
const handleClickOutside = (event) => {
  if (menuTrigger.value && !menuTrigger.value.$el.contains(event.target)) {
    showChatMenu.value = false
  }
}

// 生命周期钩子
onMounted(() => {
  window.addEventListener("resize", handleResize)
  document.addEventListener("click", handleClickOutside)
})

onUnmounted(() => {
  window.removeEventListener("resize", handleResize)
  document.removeEventListener("click", handleClickOutside)
})
</script>

<template>
  <div
    ref="topBarRef"
    class="adaptive-top-bar"
    :class="{
      'sidebar-collapsed': sidebarCollapsed,
      'small-screen': isSmallScreen,
      'medium-screen': isMediumScreen,
      'large-screen': isLargeScreen,
    }">
    <!-- 左侧：模型选择器 -->
    <div class="top-bar-left">
      <ModelSelector v-bind="modelSelectorProps" />
    </div>

    <!-- 右侧：操作按钮 -->
    <div class="top-bar-right">
      <VBtn
        icon="mdi-dots-horizontal"
        size="small"
        variant="text"
        class="chat-menu-btn"
        @click="toggleChatMenu"
        ref="menuTrigger" />

      <!-- 下拉菜单 -->
      <div v-if="showChatMenu" class="chat-dropdown-menu" @click.stop>
        <div class="menu-item" @click="handleShareConversation">
          <VIcon
            :icon="comparisonMode ? 'mdi-view-split-vertical' : 'mdi-compare'"
            size="16"
            class="menu-icon" />
          {{ comparisonMode ? '退出对比' : '模型对比' }}
        </div>
        <div class="menu-item" @click="handleTogglePhotoWall">
          <VIcon icon="mdi-image-multiple-outline" size="16" class="menu-icon" />
          图片归档
        </div>
        <div class="menu-item delete-item" @click="handleClearConversation">
          <VIcon icon="mdi-delete-sweep-outline" size="16" class="menu-icon" />
          对话清除
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.adaptive-top-bar {
  position: fixed;
  top: 0;
  left: 280px; /* 侧边栏展开时的位置 */
  right: 0;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background: transparent;
  border-bottom: none;
  z-index: 998;
  transition: left 0.3s ease, background-color 0.3s ease;
}

/* 侧边栏收缩时的样式 */
.adaptive-top-bar.sidebar-collapsed {
  left: 60px;
}

/* 左侧区域 */
.top-bar-left {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

/* 右侧区域 */
.top-bar-right {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  position: relative;
}

.chat-menu-btn {
  color: var(--app-text-secondary);
  transition: color 0.2s ease;
}

.chat-menu-btn:hover {
  color: var(--app-text-primary);
}

/* 下拉菜单样式 */
.chat-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  min-width: 160px;
  background: var(--app-bg-primary);
  border: 1px solid var(--app-border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--app-shadow);
  z-index: 1050;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 14px;
  color: var(--app-text-primary);
}

.menu-item:hover {
  background-color: var(--app-hover-bg);
}

.menu-item.delete-item {
  color: var(--app-error);
}

.menu-item.delete-item:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

.menu-icon {
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .adaptive-top-bar {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid var(--app-border-light);
  }
}

@media (max-width: 750px) {
  .adaptive-top-bar {
    left: 16px;
    height: 50px;
    padding: 0 12px;
    justify-content: center;
  }

  .adaptive-top-bar.sidebar-collapsed {
    left: 16px;
  }

  .top-bar-left {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  .top-bar-right {
    position: absolute;
    right: 12px;
  }
}

@media (max-width: 480px) {
  .adaptive-top-bar {
    height: 48px;
    padding: 0 8px;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(12px);
  }
}

/* 深色主题适配 */
[data-theme="dark"] .adaptive-top-bar {
  background: transparent;
  border-bottom: none;
}

/* 深色主题的响应式背景 */
@media (max-width: 1600px) {
  [data-theme="dark"] .adaptive-top-bar {
    background: rgba(30, 30, 30, 0.85);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
}

@media (max-width: 480px) {
  [data-theme="dark"] .adaptive-top-bar {
    background: rgba(30, 30, 30, 0.98);
  }
}
</style>
