<script setup>
import { computed, ref } from "vue"
import { useChatStore } from "@/stores/baseStore"

const chatStore = useChatStore()

// 计算属性
const photoWallImages = computed(() => chatStore.photoWallImages)
const showPhotoWall = computed(() => chatStore.showPhotoWall)

// 响应式数据
const selectedImage = ref(null)
const showImageDialog = ref(false)

// 方法
const handleImageClick = (image) => {
  // 定位到对应的消息
  const message = chatStore.scrollToMessage(image.messageId)
  if (message) {
    // 触发滚动事件
    emit("scrollToMessage", image.messageId)
  }
}

const openImagePreview = (image) => {
  selectedImage.value = image
  showImageDialog.value = true
}

const closeImagePreview = () => {
  showImageDialog.value = false
  selectedImage.value = null
}

const handleClosePhotoWall = (event) => {

  event.preventDefault()
  event.stopPropagation()

  try {
    chatStore.togglePhotoWall()
    console.log("✅ 照片墙状态切换成功，新状态:", chatStore.showPhotoWall)
  } catch (error) {
    console.error("❌ 照片墙状态切换失败:", error)
  }
}

const formatDate = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffTime = now - date
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return "今天"
  } else if (diffDays === 1) {
    return "昨天"
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return date.toLocaleDateString("zh-CN")
  }
}

// 定义事件
const emit = defineEmits(["scrollToMessage"])
</script>

<template>
  <div class="photo-wall" :class="{ show: showPhotoWall }">
    <!-- 照片墙头部 -->
    <div class="photo-wall-header">
      <div class="header-title">
        <VIcon icon="mdi-image-multiple-outline" size="20" />
        <span>照片墙</span>
        <VChip size="x-small" color="primary">{{ photoWallImages.length }}</VChip>
      </div>
      <VBtn
        icon="mdi-close"
        size="small"
        variant="text"
        class="photo-wall-close-btn"
        @click.stop="handleClosePhotoWall" />
    </div>

    <!-- 照片网格 -->
    <div v-if="photoWallImages.length > 0" class="photo-grid">
      <div
        v-for="image in photoWallImages"
        :key="image.id"
        class="photo-item"
        @click="handleImageClick(image)">
        <div class="photo-container">
          <img
            :src="image.url"
            :alt="image.name"
            class="photo-thumbnail"
            @error="handleImageError" />
          <div class="photo-overlay">
            <div class="photo-date">{{ formatDate(image.timestamp) }}</div>
            <div class="photo-actions">
              <VBtn
                icon="mdi-eye-outline"
                size="x-small"
                variant="text"
                color="white"
                @click.stop="openImagePreview(image)" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <VIcon icon="mdi-image-off-outline" size="48" color="grey-lighten-1" />
      <p class="empty-text">还没有图片</p>
    </div>

    <!-- 图片预览对话框 -->
    <VDialog v-model="showImageDialog" max-width="800" @click:outside="closeImagePreview">
      <VCard v-if="selectedImage">
        <VCardTitle class="d-flex justify-space-between align-center">
          <span>{{ selectedImage.name || "图片预览" }}</span>
          <VBtn icon="mdi-close" size="small" variant="text" @click="closeImagePreview" />
        </VCardTitle>
        <VCardText class="pa-0">
          <img :src="selectedImage.url" :alt="selectedImage.name" class="preview-image" />
        </VCardText>
        <VCardActions>
          <VBtn
            prepend-icon="mdi-map-marker-outline"
            variant="outlined"
            @click="handleImageClick(selectedImage), closeImagePreview()">
            定位到对话
          </VBtn>
          <VSpacer />
          <VBtn
            prepend-icon="mdi-download-outline"
            variant="outlined"
            @click="downloadImage(selectedImage)">
            下载
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>

<script>
export default {
  methods: {
    handleImageError(event) {
      event.target.src = "/placeholder-image.png" // 可以设置一个占位图
      console.warn("图片加载失败:", event.target.src)
    },

    downloadImage(image) {
      const link = document.createElement("a")
      link.href = image.url
      link.download = image.name || "image.png"
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
  },
}
</script>

<style scoped>
.photo-wall {
  position: fixed;
  top: 0;
  right: -320px;
  width: 320px;
  height: 100vh;
  background: var(--app-bg-primary);
  border-left: 1px solid var(--app-border-color);
  box-shadow: -2px 0 12px var(--app-shadow-light);
  transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 999;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(8px);
}

.photo-wall.show {
  right: 0;
}

.photo-wall-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 16px;
  border-bottom: 1px solid var(--app-border-light);
  background: var(--app-bg-primary);
  backdrop-filter: blur(8px);
  position: relative;
  z-index: 998;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: var(--app-text-primary);
  font-size: 16px;
}

/* 照片墙关闭按钮样式 */
.photo-wall-close-btn {
  background: var(--app-bg-secondary) !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  z-index: 999 !important;
  position: relative !important;
  pointer-events: auto !important;
}

.photo-wall-close-btn:hover {
  background: var(--app-hover-bg) !important;
  transform: scale(1.05);
}

.photo-grid {
  overflow-y: auto;
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  scrollbar-width: thin;
  scrollbar-color: var(--app-border-color) transparent;
}

.photo-grid::-webkit-scrollbar {
  width: 4px;
}

.photo-grid::-webkit-scrollbar-track {
  background: transparent;
}

.photo-grid::-webkit-scrollbar-thumb {
  background: var(--app-border-color);
  border-radius: 2px;
}

.photo-item {
  cursor: pointer;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
  background: var(--app-bg-secondary);
  border: 1px solid var(--app-border-light);
}

.photo-item:hover {
  box-shadow: 0 6px 20px var(--app-shadow-light);
  border-color: var(--app-border-color);
}

.photo-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%; /* 创建1:1的宽高比 */
  overflow: hidden;
}

.photo-thumbnail {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.photo-item:hover .photo-thumbnail {
  transform: scale(1.03);
}

.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.4) 0%,
    transparent 40%,
    transparent 60%,
    rgba(0, 0, 0, 0.6) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(1px);
}

.photo-item:hover .photo-overlay {
  opacity: 1;
}

.photo-date {
  color: white;
  font-size: 11px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  background: rgba(0, 0, 0, 0.3);
  padding: 4px 8px;
  border-radius: 6px;
  backdrop-filter: blur(4px);
  align-self: flex-start;
}

.photo-actions {
  display: flex;
  justify-content: flex-end;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 48px 32px;
  text-align: center;
}

.empty-text {
  margin-top: 16px;
  color: var(--app-text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.preview-image {
  width: 100%;
  height: auto;
  max-height: 60vh;
  object-fit: contain;
}

/* 暗黑模式特殊适配 */
[data-theme="dark"] .photo-wall {
  border-left-color: rgba(255, 255, 255, 0.1);
  box-shadow: -2px 0 16px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .photo-item {
  background: rgba(255, 255, 255, 0.02);
  border-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .photo-item:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .photo-grid::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

/* 兼容不支持aspect-ratio的浏览器 */
@supports not (aspect-ratio: 1) {
  .photo-container {
    aspect-ratio: unset;
  }
}

/* 支持aspect-ratio的现代浏览器使用更简洁的方案 */
@supports (aspect-ratio: 1) {
  .photo-container {
    height: auto;
    padding-bottom: 0;
    aspect-ratio: 1;
  }

  .photo-thumbnail {
    position: relative;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .photo-wall {
    width: 100vw;
    right: -100vw;
  }

  .photo-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    padding: 12px;
  }

  .photo-wall-header {
    padding: 16px 12px;
  }

  .header-title {
    font-size: 15px;
  }
}
</style>
