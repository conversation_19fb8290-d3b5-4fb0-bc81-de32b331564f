/**
 * AI设置相关API服务
 */

import request from '@/utils/request'
import { AI_SETTING_API } from './constants'

/**
 * AI设置API服务类
 */
export class AiSettingService {
  /**
   * 获取当前token设置
   * @returns {Promise} token设置数据
   */
  static async getTokenSetting() {
    try {
      const response = await request.get(AI_SETTING_API.GET_TOKEN)

  

      // 检查响应格式 - 根据实际响应：{"code":200,"msg":"获取成功！","data":"2048"}
      if (response.code === 200) {
        return {
          success: true,
          data: response.data, // data字段直接包含token值
          message: response.msg || '获取token设置成功'
        }
      } else {
        throw new Error(response.msg || '获取token设置失败')
      }
    } catch (error) {
      console.error('获取token设置错误:', error)
      return {
        success: false,
        error: error.message,
        message: '获取token设置失败'
      }
    }
  }

  /**
   * 设置token限制
   * @param {number} token token限制值
   * @returns {Promise} 设置结果
   */
  static async setTokenSetting(token) {
    try {
      const response = await request.post(AI_SETTING_API.SET_TOKEN, {}, {
        params: {
          'token': token
        }
      })

      // 检查响应格式
      if (response.code === 200) {
        return {
          success: true,
          data: response.data,
          message: response.msg || 'Token设置保存成功'
        }
      } else {
        throw new Error(response.msg || 'Token设置保存失败')
      }
    } catch (error) {
      console.error('设置token错误:', error)
      return {
        success: false,
        error: error.message,
        message: 'Token设置保存失败'
      }
    }
  }

  /**
   * 获取当前提示词设置
   * @param {boolean} isDeepThink 是否获取深度思考提示词
   * @returns {Promise} 提示词设置数据
   */
  static async getPromptSetting(isDeepThink = false) {
    try {
      const response = await request.get(AI_SETTING_API.GET_PROMPT, {
        params: {
          isDeepThink: isDeepThink
        }
      })

    

      // 检查响应格式 - 根据实际响应：{"code":200,"msg":"获取成功！","data":"提示词内容..."}
      if (response.code === 200) {
        return {
          success: true,
          data: response.data, // data字段直接包含提示词内容字符串
          message: response.msg || `获取${isDeepThink ? '深度思考' : '普通'}提示词设置成功`
        }
      } else {
        throw new Error(response.msg || `获取${isDeepThink ? '深度思考' : '普通'}提示词设置失败`)
      }
    } catch (error) {
      console.error(`获取${isDeepThink ? '深度思考' : '普通'}提示词设置错误:`, error)
      return {
        success: false,
        error: error.message,
        message: `获取${isDeepThink ? '深度思考' : '普通'}提示词设置失败`
      }
    }
  }

  /**
   * 设置提示词
   * @param {string} prompt 提示词内容
   * @param {boolean} isDeepThink 是否深度思考
   * @returns {Promise} 设置结果
   */
  static async setPromptSetting(prompt, isDeepThink = false) {
    try {
      const response = await request.post(AI_SETTING_API.SET_PROMPT, {}, {
        params: {
          'prompt': prompt,
          'isDeepThink': isDeepThink
        }
      })

      // 检查响应格式
      if (response.code === 200) {
        return {
          success: true,
          data: response.data,
          message: response.msg || '提示词设置保存成功'
        }
      } else {
        throw new Error(response.msg || '提示词设置保存失败')
      }
    } catch (error) {
      console.error('设置提示词错误:', error)
      return {
        success: false,
        error: error.message,
        message: '提示词设置保存失败'
      }
    }
  }

  /**
   * 批量保存AI设置
   * @param {Object} settings 设置对象
   * @param {number} settings.maxTokens 最大token限制
   * @param {string} settings.systemPrompt 系统提示词
   * @param {boolean} settings.isDeepThink 是否深度思考
   * @returns {Promise} 保存结果
   */
  static async saveAllSettings(settings) {
    try {
      const { maxTokens, systemPrompt, isDeepThink = false } = settings

      // 并发保存两个设置
      const [tokenResult, promptResult] = await Promise.all([
        this.setTokenSetting(maxTokens),
        this.setPromptSetting(systemPrompt, isDeepThink)
      ])

      // 检查两个请求的结果
      if (tokenResult.success && promptResult.success) {
        return {
          success: true,
          data: {
            token: tokenResult.data,
            prompt: promptResult.data
          },
          message: '所有设置保存成功'
        }
      } else {
        const errors = []
        if (!tokenResult.success) errors.push(tokenResult.message)
        if (!promptResult.success) errors.push(promptResult.message)

        return {
          success: false,
          error: errors.join('; '),
          message: '部分设置保存失败'
        }
      }
    } catch (error) {
      console.error('批量保存设置错误:', error)
      return {
        success: false,
        error: error.message,
        message: '保存设置时发生错误'
      }
    }
  }

  /**
   * 获取所有AI设置
   * @returns {Promise} 所有设置数据
   */
  static async getAllSettings() {
    try {
      // 并发获取三个设置
      const [tokenResult, normalPromptResult, deepThinkPromptResult] = await Promise.all([
        this.getTokenSetting(),
        this.getPromptSetting(false), // 普通提示词
        this.getPromptSetting(true)   // 深度思考提示词
      ])

      // 检查三个请求的结果
      if (tokenResult.success && normalPromptResult.success && deepThinkPromptResult.success) {
        return {
          success: true,
          data: {
            token: tokenResult.data,
            normalPrompt: normalPromptResult.data,
            deepThinkPrompt: deepThinkPromptResult.data
          },
          message: '获取所有设置成功'
        }
      } else {
        const errors = []
        if (!tokenResult.success) errors.push(tokenResult.message)
        if (!normalPromptResult.success) errors.push(normalPromptResult.message)
        if (!deepThinkPromptResult.success) errors.push(deepThinkPromptResult.message)

        return {
          success: false,
          error: errors.join('; '),
          message: '部分设置获取失败',
          data: {
            token: tokenResult.success ? tokenResult.data : null,
            normalPrompt: normalPromptResult.success ? normalPromptResult.data : null,
            deepThinkPrompt: deepThinkPromptResult.success ? deepThinkPromptResult.data : null
          }
        }
      }
    } catch (error) {
      console.error('获取所有设置错误:', error)
      return {
        success: false,
        error: error.message,
        message: '获取设置时发生错误'
      }
    }
  }
}

// 导出默认实例
export default AiSettingService
