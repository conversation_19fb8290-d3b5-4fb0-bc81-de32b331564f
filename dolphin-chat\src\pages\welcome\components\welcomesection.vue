<script setup>
import { ref } from "vue"
import logoUrl from "@/assets/logo.png"

// 响应式数据
const aiAvatar = ref(logoUrl) // 使用Dolphin AI logo
const welcomeTitle = ref("我是 Dolphin AI，很高兴见到你！")

// 方法
const handleAvatarError = () => {
  // 如果logo加载失败，回退到机器人图标
  aiAvatar.value = null
}
</script>

<template>
  <div class="welcome-section">
    <!-- AI头像 -->
    <div class="avatar-container">
      <div class="avatar-wrapper">
        <img
          v-if="aiAvatar"
          :src="aiAvatar"
          alt="Dolphin AI"
          class="ai-avatar"
          @error="handleAvatarError" />
        <VIcon v-else icon="mdi-robot-outline" size="32" color="primary" class="default-avatar" />
      </div>
    </div>

    <!-- 欢迎文字 -->
    <div class="welcome-content">
      <h1 class="welcome-title">
        {{ welcomeTitle }}
      </h1>
    </div>
  </div>
</template>

<style scoped>
.welcome-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 16px 24px;
  max-width: 600px;
  margin: 0 auto;
  gap: 16px;
}

.avatar-container {
  flex-shrink: 0;
}

.avatar-wrapper {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-avatar {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
}

.default-avatar {
  color: rgb(var(--v-theme-primary));
}

.welcome-content {
  flex: 1;
}

.welcome-title {
  font-size: 1.6rem;
  font-weight: 400;
  color: rgb(var(--v-theme-on-surface));
  margin: 0;
  line-height: 1.3;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-section {
    padding: 16px 16px;
    gap: 12px;
  }

  .welcome-title {
    font-size: 1.4rem;
  }

  .avatar-wrapper {
    width: 40px;
    height: 40px;
  }

  .ai-avatar {
    width: 40px;
    height: 40px;
  }
}
</style>
