<script setup>
import { computed } from "vue"
import ConversationItem from "./ConversationItem.vue"

// 定义props
const props = defineProps({
  conversations: {
    type: Object,
    required: true
  },
  activeConversationId: {
    type: [String, Number],
    default: null
  }
})

// 定义emits
const emit = defineEmits(["conversation-select", "conversation-rename", "conversation-delete"])

// 计算属性 - 扁平化的对话列表，用于只显示一次时间标签
const flatConversations = computed(() => {
  const result = []
  const groups = props.conversations
  const shownCategories = new Set()

  Object.keys(groups).forEach((category) => {
    const convs = groups[category]
    convs.forEach((conv, index) => {
      // 只在该分类第一次出现时显示时间标签
      const showCategoryHeader = !shownCategories.has(category) && index === 0

      if (showCategoryHeader) {
        shownCategories.add(category)
      }

      result.push({
        ...conv,
        category,
        showCategoryHeader,
      })
    })
  })

  return result
})

// 方法
const handleConversationSelect = (conversation) => {
  emit("conversation-select", conversation)
}

const handleConversationRename = (renameData) => {
  emit("conversation-rename", renameData)
}

const handleConversationDelete = (conversation) => {
  emit("conversation-delete", conversation)
}
</script>

<template>
  <div class="conversation-group">
    <!-- 会话列表内容 -->
    <template v-for="conversation in flatConversations" :key="conversation.id">
      <!-- 时间分组标题 -->
      <div v-if="conversation.showCategoryHeader" class="time-category">
        {{ conversation.category }}
      </div>

      <!-- 对话项目 -->
      <ConversationItem
        :conversation="conversation"
        :is-active="activeConversationId === conversation.id"
        @select="handleConversationSelect"
        @rename="handleConversationRename"
        @delete="handleConversationDelete"
      />
    </template>
  </div>
</template>

<style scoped>
.conversation-group {
  padding: 12px 16px;
  background-color: var(--app-bg-primary);
  transition: background-color 0.3s ease;
}

/* 时间分组标题 - DeepSeek样式 */
.time-category {
  font-size: 12px;
  font-weight: 500;
  color: var(--app-text-secondary);
  margin: 16px 0 8px 0;
  padding: 0 4px;
  opacity: 0.8;
}

.time-category:first-child {
  margin-top: 0;
}
</style>
