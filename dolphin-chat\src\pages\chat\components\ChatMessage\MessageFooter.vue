<template>
  <div class="message-footer">
    <!-- AI消息的操作按钮 -->
    <div v-if="messageType === 'ai'" class="message-actions ai-actions">
      <VBtn
        icon="mdi-content-copy"
        size="small"
        variant="text"
        @click="handleCopyMessage"
        title="复制消息" />
      <VBtn
        icon="mdi-refresh"
        size="small"
        variant="text"
        @click="handleRegenerateMessage"
        title="重新生成" />
      <!-- 显示询问图片到画布的按钮 -->
      <VBtn
        v-if="hasUserImages"
        icon="mdi-image-multiple-outline"
        size="small"
        variant="text"
        @click="handleShowUserImages"
        title="在右侧画布中显示询问的图片" />
    </div>

    <!-- 用户消息的操作按钮 -->
    <div v-if="messageType === 'user'" class="message-actions user-actions">
      <VBtn
        icon="mdi-pencil"
        size="small"
        variant="text"
        @click="handleEditMessage"
        title="编辑消息" />
    </div>

    <!-- 图片消息的操作按钮 -->
    <div v-if="messageType === 'image'" class="message-actions ai-actions">
      <VBtn
        icon="mdi-download-outline"
        size="small"
        variant="text"
        @click="handleDownloadImage"
        title="下载图片" />
      <VBtn
        icon="mdi-fullscreen-exit"
        size="small"
        variant="text"
        @click="handleOpenImagePreview"
        title="预览图片" />
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue"

// 接收props
const props = defineProps({
  messageType: {
    type: String,
    required: true,
  },
  content: {
    type: String,
    default: "",
  },
  imageUrl: {
    type: String,
    default: "",
  },
  imageName: {
    type: String,
    default: "",
  },
  // 新增：用户询问的图片信息
  userImages: {
    type: Array,
    default: () => [],
  },
})

// 定义emits
const emit = defineEmits(["download-image", "open-image-preview", "show-user-images", "regenerate-message", "edit-message"])

// 计算属性：检查是否有用户询问的图片
const hasUserImages = computed(() => {
  return props.messageType === "ai" && props.userImages && props.userImages.length > 0
})

// 事件处理
const handleCopyMessage = () => {
  navigator.clipboard.writeText(props.content)

}

const handleRegenerateMessage = () => {
  emit("regenerate-message")
}

const handleDownloadImage = () => {
  emit("download-image", props.imageUrl, props.imageName)
}

const handleOpenImagePreview = () => {
  emit("open-image-preview")
}

// 新增：处理显示用户询问图片到画布
const handleShowUserImages = () => {
  emit("show-user-images", props.userImages)
}

// 处理编辑消息
const handleEditMessage = () => {
  emit("edit-message")
}
</script>

<style scoped>
.message-footer {
  display: flex;
  align-items: center;
  margin-top: 2px;
  padding-top: 0px;
  width: 100%;
}

.message-actions {
  display: flex;
  gap: 3px;
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateX(10px);
}

/* AI消息和图片消息的按钮在左侧 */
.ai-actions {
  justify-self: flex-start;
}

.ai-actions .v-btn {
  color: #9e9e9e;
}

/* 用户消息的按钮在右侧 */
.user-actions {
  margin-left: auto;
}

.message-container:hover .message-actions {
  opacity: 1;
  transform: translateX(0);
}

.message-actions .v-btn {
  background: transparent;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  min-width: 32px;
  color: rgba(0, 0, 0, 0.6);
  transition: all 0.2s ease;
}

.message-actions .v-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-actions {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
