<script setup>
import openIconUrl from "@/assets/icons/open.svg"
import closeIconUrl from "@/assets/icons/close.svg"

// Props
const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(["toggle"])

// 方法
const handleToggle = () => {
  emit("toggle")
}
</script>

<template>
  <!-- 展开状态的收缩按钮 -->
  <VBtn
    v-if="!isCollapsed"
    variant="text"
    size="default"
    class="collapse-btn toggle-btn"
    @click="handleToggle">
    <img :src="openIconUrl" alt="收缩侧边栏" class="toggle-icon" />
  </VBtn>

  <!-- 收缩状态的展开按钮 -->
  <VBtn
    v-if="isCollapsed"
    variant="text"
    size="default"
    class="expand-btn toggle-btn"
    @click="handleToggle">
    <img :src="closeIconUrl" alt="展开侧边栏" class="toggle-icon" />
  </VBtn>
</template>

<style scoped>
/* 通用切换按钮样式 */
.toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0;
}

.toggle-btn:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.08) !important;
  transform: scale(1.05);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.collapse-btn {
  margin-top: 4px;
  border-radius: 10px !important;
  width: 36px !important;
  height: 36px !important;
  min-width: 36px !important;
}

.expand-btn {
  border-radius: 12px !important;
  width: 40px !important;
  height: 40px !important;
  min-width: 40px !important;
}

/* 切换图标样式 */
.toggle-icon {
  width: 18px;
  height: 18px;
  object-fit: contain;
  /* filter样式现在由主题CSS统一管理 */
}
</style>
