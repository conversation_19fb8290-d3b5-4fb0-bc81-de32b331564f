<script setup>
import { ref, nextTick, onMounted, onUnmounted } from "vue"

// 定义props
const props = defineProps({
  conversation: {
    type: Object,
    required: true
  },
  isActive: {
    type: Boolean,
    default: false
  }
})

// 定义emits
const emit = defineEmits(["select", "rename", "delete"])

// 响应式数据
const showMenu = ref(false)
const isRenaming = ref(false)
const renameValue = ref('')

// 方法
const selectConversation = () => {
  emit("select", props.conversation)
}

// 显示/隐藏菜单
const toggleMenu = (event) => {
  event.stopPropagation()
  showMenu.value = !showMenu.value
}

// 关闭菜单
const closeMenu = () => {
  showMenu.value = false
}

// 开始重命名
const startRename = (event) => {
  event.stopPropagation()
  isRenaming.value = true
  renameValue.value = props.conversation.title
  showMenu.value = false

  // 下一个tick后聚焦输入框
  nextTick(() => {
    const input = document.querySelector(`#rename-input-${props.conversation.id}`)
    if (input) {
      input.focus()
      input.select()
    }
  })
}

// 确认重命名
const confirmRename = () => {
  if (!renameValue.value.trim()) {
    cancelRename()
    return
  }

  const newName = renameValue.value.trim()
  emit("rename", { id: props.conversation.id, newTitle: newName })
  cancelRename()
}

// 取消重命名
const cancelRename = () => {
  isRenaming.value = false
  renameValue.value = ''
}

// 显示删除确认
const showDeleteConfirm = (event) => {
  event.stopPropagation()
  emit("delete", props.conversation)
  showMenu.value = false
}

// 处理重命名输入框的键盘事件
const handleRenameKeydown = (event) => {
  if (event.key === 'Enter') {
    confirmRename()
  } else if (event.key === 'Escape') {
    cancelRename()
  }
}

// 全局点击事件处理器
const handleGlobalClick = (event) => {
  if (!event.target.closest('.conversation-actions')) {
    closeMenu()
  }
}

// 生命周期钩子
onMounted(() => {
  document.addEventListener('click', handleGlobalClick)
})

onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick)
})

// 暴露方法给父组件
defineExpose({
  closeMenu
})
</script>

<template>
  <div
    class="conversation-item"
    :class="{ active: isActive }"
    @click="selectConversation">

    <!-- 重命名模式 -->
    <div v-if="isRenaming" class="rename-container">
      <input
        :id="`rename-input-${conversation.id}`"
        v-model="renameValue"
        class="rename-input"
        @keydown="handleRenameKeydown"
        @blur="confirmRename"
        @click.stop
      />
    </div>

    <!-- 正常显示模式 -->
    <div v-else class="conversation-content">
      <div class="conversation-title">{{ conversation.title }}</div>

      <!-- 三点菜单按钮 -->
      <div class="conversation-actions">
        <button
          class="menu-button"
          @click="toggleMenu"
          :class="{ active: showMenu }"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <circle cx="12" cy="5" r="2"/>
            <circle cx="12" cy="12" r="2"/>
            <circle cx="12" cy="19" r="2"/>
          </svg>
        </button>

        <!-- 下拉菜单 -->
        <div
          v-if="showMenu"
          class="dropdown-menu"
          @click.stop
        >
          <div class="menu-item" @click="startRename">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
            </svg>
            重命名
          </div>
          <div class="menu-item delete" @click="showDeleteConfirm">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
            </svg>
            删除
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.conversation-item {
  padding: 8px 12px;
  margin-bottom: 2px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.15s ease;
  background-color: transparent;
  position: relative;
  border: none;
}

.conversation-item:hover {
  background-color: var(--app-hover-bg);
}

.conversation-item.active {
  background-color: var(--app-active-bg);
}

.conversation-title {
  font-size: 13px;
  font-weight: 400;
  color: var(--app-text-primary);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.3s ease;
  flex: 1;
  min-width: 0;
}

.conversation-item.active .conversation-title {
  color: var(--v-theme-primary);
  font-weight: 500;
}

/* 对话内容布局 */
.conversation-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
}

/* 重命名容器 */
.rename-container {
  width: 100%;
}

.rename-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid var(--v-theme-primary);
  border-radius: 4px;
  font-size: 13px;
  font-weight: 400;
  color: var(--app-text-primary);
  background-color: var(--app-bg-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
  transition: all 0.3s ease;
}

/* 操作按钮区域 */
.conversation-actions {
  position: relative;
  flex-shrink: 0;
}

.menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  border-radius: 3px;
  cursor: pointer;
  color: var(--app-text-secondary);
  opacity: 0;
  transition: all 0.15s ease;
}

.conversation-item:hover .menu-button {
  opacity: 1;
}

.menu-button:hover,
.menu-button.active {
  background-color: var(--app-hover-bg);
  color: var(--app-text-primary);
}

.menu-button.active {
  opacity: 1;
}

/* 下拉菜单 */
.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 100px;
  background: var(--app-bg-primary);
  border: 1px solid var(--app-border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px var(--app-shadow);
  z-index: 1000;
  overflow: hidden;
  margin-top: 2px;
  transition: all 0.3s ease;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  font-size: 12px;
  color: var(--app-text-primary);
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.3s ease;
  gap: 6px;
}

.menu-item:hover {
  background-color: var(--app-hover-bg);
}

.menu-item.delete {
  color: #d32f2f;
}

.menu-item.delete:hover {
  background-color: #ffebee;
}

.menu-item svg {
  flex-shrink: 0;
}
</style>
