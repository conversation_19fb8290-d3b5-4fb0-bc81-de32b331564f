<script setup>
// 导入SVG图标
import deepIconUrl from "@/assets/icons/Deep.svg"
import searchIconUrl from "@/assets/icons/search.svg"

// 定义 props
defineProps({
  deepThinkActive: {
    type: Boolean,
    default: false,
  },
  webSearchActive: {
    type: Boolean,
    default: false,
  },
  showPhotoArea: {
    type: Boolean,
    default: false,
  },
  hasImages: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  isStreaming: {
    type: Boolean,
    default: false,
  },
})

// 定义 emits
defineEmits([
  'deep-think',
  'web-search',
  'toggle-photo',
  'attachment',
  'send',
  'stop',
])
</script>

<template>
  <div class="bottom-actions">
    <!-- 深度思考按钮 -->
    <VTooltip text="启用深度思考模式，获得更详细的分析" location="top">
      <template #activator="{ props }">
        <VBtn
          v-bind="props"
          :variant="deepThinkActive ? 'flat' : 'outlined'"
          size="small"
          :class="['feature-btn', { 'feature-btn--active': deepThinkActive }]"
          @click="$emit('deep-think')">
          <img
            :src="deepIconUrl"
            alt="深度思考"
            class="feature-icon mr-1"
            :class="{ 'feature-icon--active': deepThinkActive }" />
          深度思考 (R1)
        </VBtn>
      </template>
    </VTooltip>

    <!-- 联网搜索按钮 -->
    <VTooltip text="启用联网搜索，获取最新信息" location="top">
      <template #activator="{ props }">
        <VBtn
          v-bind="props"
          :variant="webSearchActive ? 'flat' : 'outlined'"
          size="small"
          :class="['feature-btn', { 'feature-btn--active': webSearchActive }]"
          @click="$emit('web-search')">
          <img
            :src="searchIconUrl"
            alt="联网搜索"
            class="feature-icon mr-1"
            :class="{ 'feature-icon--active': webSearchActive }" />
          联网搜索
        </VBtn>
      </template>
    </VTooltip>

    <!-- 右侧按钮组 -->
    <div class="right-actions">
      <!-- 图像按钮 -->
      <VTooltip text="上传图片" location="top">
        <template #activator="{ props }">
          <VBtn
            v-bind="props"
            icon
            variant="text"
            size="small"
            class="action-btn"
            :class="{ 'active': showPhotoArea || hasImages }"
            @click="$emit('toggle-photo')">
            <VIcon
              icon="mdi-camera"
              size="20"
              :color="showPhotoArea || hasImages ? 'rgb(102, 126, 234)' : 'grey-lighten-1'" />
          </VBtn>
        </template>
      </VTooltip>

      <!-- 附件按钮 -->
      <VTooltip text="上传文件" location="top">
        <template #activator="{ props }">
          <VBtn
            v-bind="props"
            icon
            variant="text"
            size="small"
            class="action-btn"
            @click="$emit('attachment')">
            <VIcon
              icon="mdi-paperclip"
              size="20"
              color="grey-lighten-1" />
          </VBtn>
        </template>
      </VTooltip>

      <!-- 停止生成按钮 (流式响应时显示) -->
      <VTooltip v-if="isStreaming" text="停止生成" location="top">
        <template #activator="{ props }">
          <VBtn
            v-bind="props"
            icon
            variant="text"
            size="small"
            class="stop-btn"
            @click="$emit('stop')">
            <VIcon
              icon="mdi-stop"
              size="20"
              color="white" />
          </VBtn>
        </template>
      </VTooltip>

      <!-- 发送按钮 -->
      <VTooltip v-if="!isStreaming" :text="disabled || isLoading ? '请输入消息内容' : '发送消息 (Enter)'" location="top">
        <template #activator="{ props }">
          <VBtn
            v-bind="props"
            :disabled="disabled || isLoading"
            :loading="isLoading"
            icon
            variant="text"
            size="small"
            class="send-btn"
            @click="$emit('send')">
            <VIcon
              icon="mdi-arrow-up"
              size="20"
              :color="!disabled && !isLoading ? 'white' : 'grey-lighten-1'" />
          </VBtn>
        </template>
      </VTooltip>
    </div>
  </div>
</template>

<style scoped>
/* SVG图标样式 */
.feature-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0) saturate(100%) invert(40%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
  transition: filter 0.2s ease;
}

.feature-icon--active {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7500%) hue-rotate(109deg) brightness(105%) contrast(105%);
}

.action-icon {
  width: 20px;
  height: 20px;
  /* filter样式现在由主题CSS统一管理 */
}

/* 深色主题下的图标适配现在由主题CSS统一管理 */

[data-theme="dark"] .action-icon {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7500%) hue-rotate(109deg) brightness(105%) contrast(105%);
}

[data-theme="dark"] .action-btn:hover .action-icon {
  filter: brightness(0) saturate(100%) invert(59%) sepia(98%) saturate(1946%) hue-rotate(213deg) brightness(97%) contrast(86%);
}
.bottom-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px 12px 20px;
  gap: 12px;
  background: transparent !important;
}

/* 深色主题下的底部操作区域 */
[data-theme="dark"] .bottom-actions {
  background: transparent !important;
}

.feature-btn {
  border-radius: 20px;
  text-transform: none;
  font-size: 13px;
  height: 36px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  transition: all 0.2s ease;
  font-weight: 500;
  background: var(--app-bg-primary);
  color: var(--app-text-primary);
  box-shadow: 0 1px 3px var(--app-shadow-light);
}

.feature-btn:hover {
  border-color: rgba(102, 126, 234, 0.4);
  background: var(--app-hover-bg);
}

.feature-btn--active {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
  border-color: transparent !important;
  color: white !important;
  font-weight: 600 !important;
  box-shadow: 0 3px 12px rgba(59, 130, 246, 0.4) !important;
}

.feature-btn--active:hover {
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%) !important;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.5) !important;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.action-btn {
  width: 36px;
  height: 36px;
  min-width: 36px;
  border-radius: 50%;
  background: var(--app-bg-primary);
  transition: background-color 0.2s ease;
  box-shadow: 0 1px 3px var(--app-shadow-light);
}

.action-btn:hover {
  background: var(--app-hover-bg);
}

.action-btn.active {
  background: rgba(102, 126, 234, 0.1);
}

.action-btn.active:hover {
  background: rgba(102, 126, 234, 0.2);
}

.send-btn {
  width: 40px;
  height: 40px;
  min-width: 40px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 3px 12px rgba(59, 130, 246, 0.4);
  border: none;
}

.send-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.5);
}

.send-btn:disabled {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  transform: none;
  box-shadow: 0 2px 8px rgba(148, 163, 184, 0.3);
  border: none;
}

.send-btn:disabled:hover {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  transform: none;
  box-shadow: 0 2px 8px rgba(148, 163, 184, 0.3);
}

.stop-btn {
  width: 40px;
  height: 40px;
  min-width: 40px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 3px 12px rgba(239, 68, 68, 0.4);
  border: none;
}

.stop-btn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.5);
}

/* 深色主题适配 */
[data-theme="dark"] .feature-btn {
  border-color: rgba(255, 255, 255, 0.2);
  background: var(--app-bg-secondary);
  color: var(--app-text-primary);
}

[data-theme="dark"] .feature-btn:hover {
  border-color: rgba(255, 255, 255, 0.4);
  background: var(--app-hover-bg);
}

[data-theme="dark"] .action-btn {
  background: var(--app-bg-secondary);
}

[data-theme="dark"] .action-btn:hover {
  background: var(--app-hover-bg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bottom-actions {
    padding: 4px 12px 8px 12px;
    flex-wrap: wrap;
    gap: 6px;
  }

  .feature-btn {
    font-size: 12px;
    height: 28px;
  }
}
</style>
