<script setup>
import { ref, provide, onMounted, onUnmounted } from "vue"
import Sidebar from "@/components/layout/sidebar.vue"
import MainContent from "@/components/layout/maincontent.vue"
import ChatInput from "@/pages/chat/components/ChatInput/index.vue"
import GlobalLoadingDialog from "@/components/common/GlobalLoadingDialog.vue"
import { useChatStore } from "@/stores/baseStore"
import { useAuthStore } from "@/stores/authstore"

// 侧边栏状态
const sidebarCollapsed = ref(false)
const authStore = useAuthStore()

// 使用聊天store
const chatStore = useChatStore()

// 提供侧边栏状态给子组件
provide("sidebarCollapsed", sidebarCollapsed)

// 检查是否为移动端
const isMobile = () => {
  return window.innerWidth <= 768
}

// 处理窗口大小变化
const handleResize = () => {
  if (isMobile()) {
    // 移动端自动收缩侧边栏
    sidebarCollapsed.value = true
  } else {
    // 桌面端可以展开侧边栏（可选）
    // sidebarCollapsed.value = false
  }
}

// 处理侧边栏切换
const handleSidebarToggle = (isCollapsed) => {
  sidebarCollapsed.value = isCollapsed
}

// 处理收缩侧边栏事件
const handleCollapseSidebar = () => {
  sidebarCollapsed.value = true
}

// 生命周期钩子
onMounted(() => {
  // 初始化时检查屏幕大小
  handleResize()
  // 监听窗口大小变化
  window.addEventListener("resize", handleResize)
  // 监听收缩侧边栏事件
  window.addEventListener("collapse-sidebar", handleCollapseSidebar)

  // 确保token检查正在运行
  if (authStore.isLoggedIn) {
    authStore.startTokenCheck()
  }
})

onUnmounted(() => {
  window.removeEventListener("resize", handleResize)
  window.removeEventListener("collapse-sidebar", handleCollapseSidebar)
})
</script>

<template>
  <VApp>
    <!-- 主体布局 -->
    <VMain>
      <div class="app-layout">
        <!-- 左侧边栏 -->
        <Sidebar @sidebar-toggle="handleSidebarToggle" />

        <!-- 主内容区域 -->
        <div class="content-area">
          <MainContent />
          <ChatInput :sidebar-collapsed="sidebarCollapsed" />
        </div>
      </div>
    </VMain>

    <!-- 全局加载弹窗 -->
    <GlobalLoadingDialog
      v-model="chatStore.showGlobalLoading"
      :loading-text="chatStore.globalLoadingText"
      :sub-text="chatStore.globalLoadingSubText"
      :show-progress="chatStore.showGlobalProgress"
      :progress="chatStore.globalLoadingProgress" />
  </VApp>
</template>

<style scoped>
.app-layout {
  display: flex;
  height: 100vh;
  overflow: hidden; /* 防止外层滚动条 */
}

.content-area {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100vh;
  overflow: hidden; /* 防止外层滚动条 */
  /* 移除min-height，使用固定height */
}

/* 响应式设计 */
/* 移动端样式已在各个组件中单独处理 */
</style>
