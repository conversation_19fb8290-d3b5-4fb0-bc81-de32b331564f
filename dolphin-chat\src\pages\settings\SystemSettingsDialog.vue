<script setup>
import { ref, computed } from "vue"
import GeneralSettings from "./components/GeneralSettings.vue"
import AccountManagement from "./components/AccountManagement.vue"
import ServiceAgreement from "./components/ServiceAgreement.vue"

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(["update:modelValue"])

// 响应式数据
const activeSettingsTab = ref("general")

// 计算属性
const dialogOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value)
})

// 方法
const closeDialog = () => {
  dialogOpen.value = false
}
</script>

<template>
  <!-- 系统设置弹窗 - DeepSeek 风格 -->
  <VDialog v-model="dialogOpen" max-width="600" persistent>
    <VCard class="deepseek-settings-dialog">
      <!-- 对话框头部 -->
      <div class="deepseek-header">
        <h2 class="deepseek-title">系统设置</h2>
        <VBtn
          icon="mdi-close"
          variant="text"
          size="small"
          class="close-btn"
          @click="closeDialog" />
      </div>

      <!-- 选项卡导航 -->
      <div class="deepseek-tabs">
        <div
          class="deepseek-tab"
          :class="{ active: activeSettingsTab === 'general' }"
          @click="activeSettingsTab = 'general'">
          通用设置
        </div>
        <div
          class="deepseek-tab"
          :class="{ active: activeSettingsTab === 'account' }"
          @click="activeSettingsTab = 'account'">
          账号管理
        </div>
        <div
          class="deepseek-tab"
          :class="{ active: activeSettingsTab === 'service' }"
          @click="activeSettingsTab = 'service'">
          服务协议
        </div>
      </div>

      <!-- 选项卡内容 -->
      <div class="deepseek-content">
        <!-- 通用设置 -->
        <GeneralSettings v-if="activeSettingsTab === 'general'" />

        <!-- 账号管理 -->
        <AccountManagement v-if="activeSettingsTab === 'account'" />

        <!-- 服务协议 -->
        <ServiceAgreement v-if="activeSettingsTab === 'service'" />
      </div>
    </VCard>
  </VDialog>
</template>

<style scoped>
/* DeepSeek 风格系统设置弹窗样式 - 简洁版本 */
.deepseek-settings-dialog {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px var(--app-shadow);
  background: var(--dialog-content-bg);
}

.deepseek-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--dialog-header-bg);
  border-bottom: 1px solid var(--dialog-border-color);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.close-btn {
  color: var(--app-text-secondary) !important;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.close-btn:hover {
  background-color: var(--app-hover-bg);
  color: var(--app-text-primary) !important;
}

.deepseek-content {
  padding: 24px;
  background: var(--app-bg-primary);
  min-height: 320px;
  transition: background-color 0.3s ease;
}



/* 更新现有样式以使用CSS变量 */
.deepseek-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--app-text-primary);
  margin: 0;
  transition: color 0.3s ease;
}

.deepseek-tabs {
  display: flex;
  background: var(--dialog-tab-bg);
  border-bottom: 1px solid var(--dialog-border-color);
   border-top: 1px solid var(--dialog-border-color);
  position: relative;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.deepseek-tab {
  flex: 1;
  padding: 14px 20px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: var(--app-text-secondary);
  cursor: pointer;
  background-color: transparent;
  transition: all 0.3s ease;
  position: relative;
  border-bottom: 2px solid transparent;
}

.deepseek-tab:hover {
  color: var(--v-theme-primary);
  background: var(--app-hover-bg);
}

.deepseek-tab.active {
  color: var(--v-theme-primary);
  background: var(--app-bg-primary);
  font-weight: 600;
  border-bottom: 2px solid var(--v-theme-primary);
  box-shadow: 0 -1px 0 0 var(--app-bg-primary);
}


</style>
