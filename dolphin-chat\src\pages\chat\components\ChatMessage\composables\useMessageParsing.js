import { computed } from 'vue'
import { renderMarkdown } from '@/utils/markdown'

/**
 * AI消息解析相关的 composable
 */
export function useMessageParsing(message) {
  // 解析AI消息中的think和answer标签（支持实时流式解析）
  const parsedAIMessage = computed(() => {
    if (!message.value || message.value.type !== 'ai' || !message.value.content) {
      return null
    }

    const content = message.value.content
    const parts = []
    let lastIndex = 0
    let thinkingTime = 0

    // 匹配完整的标签对
    const completeTagRegex = /<(think|answer)>([\s\S]*?)<\/\1>/g
    let match

    while ((match = completeTagRegex.exec(content)) !== null) {
      // 添加标签前的普通文本
      if (match.index > lastIndex) {
        const beforeText = content.slice(lastIndex, match.index).trim()
        if (beforeText) {
          parts.push({
            type: "text",
            content: beforeText,
            htmlContent: renderMarkdown(beforeText),
          })
        }
      }

      // 添加标签内容
      const partContent = match[2].trim()
      parts.push({
        type: match[1], // 'think' 或 'answer'
        content: partContent,
        htmlContent: match[1] === 'answer' ? renderMarkdown(partContent) : partContent,
        complete: true,
      })

      // 如果是think标签，估算思考时间（基于内容长度）
      if (match[1] === "think") {
        // 简单的时间估算：每100个字符约1秒，最少3秒，最多30秒
        const estimatedTime = Math.min(Math.max(Math.ceil(partContent.length / 100), 3), 30)
        thinkingTime = estimatedTime
      }

      lastIndex = match.index + match[0].length
    }

    // 处理剩余内容，检查是否有未完成的标签
    if (lastIndex < content.length) {
      const remainingText = content.slice(lastIndex)

      // 检查是否有未完成的开始标签
      const incompleteStartMatch = remainingText.match(/<(think|answer)>([\s\S]*)$/)
      if (incompleteStartMatch) {
        // 找到未完成的开始标签
        const tagType = incompleteStartMatch[1]
        const tagContent = incompleteStartMatch[2]

        // 添加标签前的普通文本
        const beforeTag = remainingText.slice(0, incompleteStartMatch.index).trim()
        if (beforeTag) {
          parts.push({
            type: "text",
            content: beforeTag,
            htmlContent: renderMarkdown(beforeTag),
          })
        }

        // 添加未完成的标签内容
        if (tagContent) {
          parts.push({
            type: tagType,
            content: tagContent,
            htmlContent: tagType === 'answer' ? renderMarkdown(tagContent) : tagContent,
            complete: false, // 标记为未完成
          })

          // 如果是think标签，估算思考时间
          if (tagType === "think") {
            const estimatedTime = Math.min(Math.max(Math.ceil(tagContent.length / 100), 3), 30)
            thinkingTime = estimatedTime
          }
        }
      } else {
        // 没有未完成的标签，作为普通文本处理
        const trimmedText = remainingText.trim()
        if (trimmedText) {
          parts.push({
            type: "text",
            content: trimmedText,
            htmlContent: renderMarkdown(trimmedText),
          })
        }
      }
    }

    // 如果没有匹配到任何标签，返回原始内容
    if (parts.length === 0) {
      parts.push({
        type: "text",
        content: content,
        htmlContent: renderMarkdown(content),
      })
    }

    return {
      parts,
      thinkingTime,
      hasThinking: parts.some((part) => part.type === "think"),
    }
  })

  return {
    parsedAIMessage,
  }
}
