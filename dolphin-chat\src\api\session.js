/**
 * 会话相关API服务
 */

import request from '@/utils/request'
import { SESSION_API } from './constants'

/**
 * 会话API服务类
 */
export class SessionService {
  /**
   * 获取会话列表
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @param {string} params.keyword 搜索关键词
   * @returns {Promise} 会话列表
   */
  static async getSessionList(params = {}) {
    try {
      const response = await request.get(SESSION_API.GET_LIST, params)

     

      // 检查响应格式
      if (response.code === 200 && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.msg || '获取会话列表成功'
        }
      } else {
        throw new Error(response.msg || '获取会话列表失败')
      }
    } catch (error) {
      console.error('获取会话列表错误:', error)
      return {
        success: false,
        error: error.message,
        message: '获取会话列表失败',
        data: []
      }
    }
  }

  /**
   * 创建新会话
   * @param {Object} sessionData 会话数据
   * @param {string} sessionData.name 会话名称
   * @param {string} sessionData.description 会话描述
   * @returns {Promise} 创建结果
   */
  static async createSession(sessionData = {}) {
    try {
      const response = await request.post(SESSION_API.CREATE, sessionData)

      return {
        success: true,
        data: response,
        message: '创建会话成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '创建会话失败'
      }
    }
  }

  /**
   * 删除会话
   * @param {string|number} sessionId 会话ID
   * @returns {Promise} 删除结果
   */
  static async deleteSession(sessionId) {
    try {
      const response = await request.delete(SESSION_API.DELETE, {
        params: {
          id: sessionId
        }
      })

      return {
        success: true,
        data: response,
        message: '删除会话成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '删除会话失败'
      }
    }
  }

  /**
   * 重命名会话
   * @param {string|number} sessionId 会话ID
   * @param {string} newName 新名称
   * @returns {Promise} 重命名结果
   */
  static async renameSession(sessionId, newName) {
    try {
      const response = await request.post('/v1/session/edit', {
        id: sessionId,
        name: newName
      })

      // 接口返回200，直接成功
      if (response.code === 200) {
        return { success: true, message: '重命名成功' }
      } else {
        throw new Error(response.msg || '重命名失败')
      }
    } catch (error) {
      console.error('重命名会话错误:', error)
      return {
        success: false,
        error: error.message,
        message: '重命名会话失败'
      }
    }
  }

  /**
   * 获取会话详情
   * @param {string|number} sessionId 会话ID
   * @returns {Promise} 会话详情
   */
  static async getSessionDetail(sessionId) {
    try {
      const response = await request.get(`${SESSION_API.GET_DETAIL}/${sessionId}`)

      return {
        success: true,
        data: response,
        message: '获取会话详情成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '获取会话详情失败'
      }
    }
  }

  /**
   * 格式化会话数据用于时间分组
   * @param {Array} sessions 原始会话数据
   * @returns {Object} 按时间分组的会话数据
   */
  static formatSessionsForTimeGroup(sessions) {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)

    const groups = {}

    sessions.forEach((session) => {
      // 解析时间字符串，支持多种格式
      let sessionDate
      if (session.createTime) {
        sessionDate = new Date(session.createTime)
      } else if (session.updateTime) {
        sessionDate = new Date(session.updateTime)
      } else {
        sessionDate = new Date() // 默认当前时间
      }

      // 检查日期是否有效
      if (isNaN(sessionDate.getTime())) {
        console.warn('无效的日期格式:', session.createTime || session.updateTime)
        sessionDate = new Date()
      }

      const sessionDateOnly = new Date(sessionDate.getFullYear(), sessionDate.getMonth(), sessionDate.getDate())

      let category
      if (sessionDateOnly >= today) {
        category = "今天"
      } else if (sessionDateOnly >= sevenDaysAgo) {
        category = "7天内"
      } else if (sessionDateOnly >= thirtyDaysAgo) {
        category = "30天内"
      } else {
        // 使用年月格式
        category = `${sessionDate.getFullYear()}-${String(sessionDate.getMonth() + 1).padStart(2, "0")}`
      }

      if (!groups[category]) {
        groups[category] = []
      }

      // 格式化会话数据
      groups[category].push({
        id: session.id,
        title: session.name || '未命名会话',
        date: sessionDate,
        category,
        userId: session.userId,
        createTime: session.createTime,
        updateTime: session.updateTime
      })
    })

    // 按时间顺序排序分组
    const sortedGroups = {}
    const order = ["今天", "7天内", "30天内"]

    // 先添加固定顺序的分组
    order.forEach((key) => {
      if (groups[key]) {
        // 按时间倒序排序组内会话
        groups[key].sort((a, b) => new Date(b.date) - new Date(a.date))
        sortedGroups[key] = groups[key]
      }
    })

    // 再添加年月分组，按时间倒序
    Object.keys(groups)
      .filter((key) => !order.includes(key))
      .sort((a, b) => b.localeCompare(a))
      .forEach((key) => {
        // 按时间倒序排序组内会话
        groups[key].sort((a, b) => new Date(b.date) - new Date(a.date))
        sortedGroups[key] = groups[key]
      })

    return sortedGroups
  }
}

export default SessionService
