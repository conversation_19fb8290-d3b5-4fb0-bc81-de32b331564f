<template>
  <router-view />
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'
import { useTheme } from '@/composables/useTheme'
import { useAuthStore } from '@/stores/authstore'
import { cleanupFontSettings } from '@/utils/cleanupFontSettings'

// 初始化主题系统
const { initTheme } = useTheme()
const authStore = useAuthStore()

// 处理token过期事件
const handleTokenExpired = (event) => {
  console.warn('🔒 收到token过期事件:', event.detail.message)
  // 确保认证状态被清除
  authStore.handleTokenExpired()
}

onMounted(async () => {
  // 清理字体设置残留数据
  cleanupFontSettings()

  // 在应用挂载时初始化主题
  initTheme()

  // 初始化认证状态和token检查（异步）
  await authStore.initAuth()

  // 监听全局token过期事件
  window.addEventListener('token-expired', handleTokenExpired)
})

onUnmounted(() => {
  // 应用卸载时停止token检查
  authStore.stopTokenCheck()

  // 移除事件监听器
  window.removeEventListener('token-expired', handleTokenExpired)
})
</script>
