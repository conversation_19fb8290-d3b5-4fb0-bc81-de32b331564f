<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue"
import { SessionService } from "@/api/session"
import ConversationGroup from "./components/ConversationGroup.vue"
import ConversationStates from "./components/ConversationStates.vue"
import DeleteConfirmDialog from "./components/DeleteConfirmDialog.vue"

// 定义emits
const emit = defineEmits(["conversation-select", "conversation-rename", "conversation-delete"])

// 响应式数据
const conversations = ref([])
const loading = ref(false)
const error = ref(null)
const activeConversation = ref(null)

// 删除确认弹窗相关
const showDeleteDialog = ref(false)
const deleteTargetId = ref(null)
const deleteTargetTitle = ref("")

// 计算属性 - 按日期分组对话，使用API服务的格式化方法
const groupedConversations = computed(() => {
  if (!conversations.value || conversations.value.length === 0) {
    return {}
  }
  return SessionService.formatSessionsForTimeGroup(conversations.value)
})

// 获取会话列表数据
const fetchSessions = async () => {
  loading.value = true
  error.value = null

  try {
    const result = await SessionService.getSessionList()

    if (result.success) {
      conversations.value = result.data || []
    } else {
      error.value = result.message || "获取会话列表失败"
      console.error("获取会话列表失败:", result.error)
    }
  } catch (err) {
    error.value = "网络错误，请稍后重试"
    console.error("获取会话列表异常:", err)
  } finally {
    loading.value = false
  }
}

// 方法
const selectConversation = (conversation) => {
  activeConversation.value = conversation.id
  emit("conversation-select", conversation)
}

// 处理重命名
const handleConversationRename = async (renameData) => {
  const { id: conversationId, newTitle: newName } = renameData

  try {
    const result = await SessionService.renameSession(conversationId, newName)

    if (result.success) {
      // API返回200时，直接替换名字，无繁琐步骤
      const conversation = conversations.value.find((c) => c.id === conversationId)
      if (conversation) {
        conversation.title = newName // 直接替换
      }
      emit("conversation-rename", { id: conversationId, newTitle: newName })

      // 重命名成功后，重新调用列表接口获取最新数据
      await fetchSessions()
    } else {
      alert("重命名失败: " + result.message)
    }
  } catch (error) {
    console.error("重命名异常:", error)
    alert("重命名失败，请稍后重试")
  }
}

// 显示删除确认弹窗
const handleConversationDelete = (conversation) => {
  deleteTargetId.value = conversation.id
  deleteTargetTitle.value = conversation.title
  showDeleteDialog.value = true
}

// 确认删除对话
const confirmDelete = async () => {
  if (!deleteTargetId.value) return

  try {
    const result = await SessionService.deleteSession(deleteTargetId.value)

    if (result.success) {
      // 更新本地数据
      const index = conversations.value.findIndex((c) => c.id === deleteTargetId.value)
      if (index > -1) {
        conversations.value.splice(index, 1)
      }
      emit("conversation-delete", deleteTargetId.value)

      // 如果删除的是当前活跃对话，清除活跃状态
      if (activeConversation.value === deleteTargetId.value) {
        activeConversation.value = null
      }
    } else {
      console.error("删除会话失败:", result.error)
      alert("删除失败: " + result.message)
    }
  } catch (error) {
    console.error("删除会话异常:", error)
    alert("删除失败，请稍后重试")
  }

  // 关闭弹窗并重置状态
  closeDeleteDialog()
}

// 关闭删除确认弹窗
const closeDeleteDialog = () => {
  showDeleteDialog.value = false
  deleteTargetId.value = null
  deleteTargetTitle.value = ""
}

// 处理会话重命名事件
const handleSessionRenamed = (event) => {
  const { sessionId, newTitle } = event.detail
  console.log("📝 收到会话重命名事件:", sessionId, newTitle)

  // 更新本地会话列表中的标题
  const conversation = conversations.value.find((c) => c.id === sessionId)
  if (conversation) {
    conversation.name = newTitle
    console.log("✅ 本地会话标题已更新")
  } else {
    // 如果本地没有找到，重新获取会话列表
    console.log("🔄 本地未找到会话，重新获取会话列表")
    fetchSessions()
  }
}

// 生命周期钩子
onMounted(() => {
  // 组件挂载时获取会话列表
  fetchSessions()

  // 监听会话重命名事件
  if (typeof window !== "undefined") {
    window.addEventListener("session-renamed", handleSessionRenamed)
  }
})

onUnmounted(() => {
  // 组件卸载时移除事件监听
  if (typeof window !== "undefined") {
    window.removeEventListener("session-renamed", handleSessionRenamed)
  }
})

// 暴露方法给父组件
defineExpose({
  fetchSessions,
})
</script>

<template>
  <!-- 对话历史 - DeepSeek样式 -->
  <div class="conversation-history">
    <!-- 状态显示组件 -->
    <ConversationStates
      v-if="loading || error || conversations.length === 0"
      :loading="loading"
      :error="error"
      :is-empty="conversations.length === 0"
      @retry="fetchSessions" />

    <!-- 会话分组组件 -->
    <ConversationGroup
      v-else
      :conversations="groupedConversations"
      :active-conversation-id="activeConversation"
      @conversation-select="selectConversation"
      @conversation-rename="handleConversationRename"
      @conversation-delete="handleConversationDelete" />

    <!-- 删除确认弹窗组件 -->
    <DeleteConfirmDialog
      v-model:show="showDeleteDialog"
      :target-title="deleteTargetTitle"
      @confirm="confirmDelete"
      @cancel="closeDeleteDialog" />
  </div>
</template>

<style scoped>
.conversation-history {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  padding: 0;
  background-color: var(--app-bg-primary);
  transition: background-color 0.3s ease;
}

/* 滚动条样式 - 简洁设计 */
.conversation-history::-webkit-scrollbar {
  width: 4px;
}

.conversation-history::-webkit-scrollbar-track {
  background: transparent;
}

.conversation-history::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  transition: background 0.3s ease;
}

.conversation-history::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}
</style>
