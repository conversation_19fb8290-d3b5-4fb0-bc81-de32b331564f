<script setup>
import { ref, onMounted } from "vue"
import { UserService } from "@/api/user"
import ChangePasswordDialog from "./ChangePasswordDialog.vue"

// 账号信息数据
const accountInfo = ref({
  username: "",
  nickname: "",
  email: "",
  phone: "",
  registerDate: "",
})

// 加载状态
const loading = ref(false)
const error = ref("")

// 密码修改弹窗状态
const showPasswordDialog = ref(false)

// 获取用户信息
const fetchUserInfo = async () => {
  loading.value = true
  error.value = ""

  try {
    const result = await UserService.getUserProfile()

    if (result.success && result.data) {
      // 根据API返回的数据结构更新账号信息
      const userData = result.data
      accountInfo.value = {
        username: userData.username || "",
        nickname: userData.nickname || userData.name || "",
        email: userData.email || "",
        phone: userData.phone || userData.mobile || "",
        registerDate: userData.registerDate || userData.createTime || "",
      }

    } else {
      throw new Error(result.message || "获取用户信息失败")
    }
  } catch (err) {
    error.value = err.message || "获取用户信息失败"
    console.error("获取用户信息错误:", err)
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取用户信息
onMounted(() => {
  fetchUserInfo()
})

// 方法
const handleChangePassword = () => {
  showPasswordDialog.value = true
}

const handleBindEmail = () => {
  console.log("绑定邮箱")
  // TODO: 实现绑定邮箱逻辑
}

const handleDeleteAccount = () => {
  console.log("注销账号")
  // TODO: 实现注销账号逻辑
}

// 重新获取用户信息
const handleRefresh = () => {
  fetchUserInfo()
}
</script>

<template>
  <div class="tab-panel">
    <!-- 错误提示 -->
    <div v-if="error" class="error-message">
      <VAlert type="error" variant="tonal" class="mb-4">
        {{ error }}
        <template #append>
          <VBtn size="small" variant="text" @click="handleRefresh"> 重试 </VBtn>
        </template>
      </VAlert>
    </div>

    <div class="account-section">
      <div class="section-header">
        <h3 class="section-title">账号信息</h3>
        <VBtn
          size="small"
          variant="text"
          :loading="loading"
          @click="handleRefresh"
          class="refresh-btn">
          刷新
        </VBtn>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading && !accountInfo.username" class="loading-container">
        <VProgressCircular indeterminate size="24" class="mr-2" />
        <span>正在获取用户信息...</span>
      </div>

      <!-- 用户信息 -->
      <div v-else class="info-container">
        <div class="info-row">
          <span class="info-label">用户名</span>
          <span class="info-value">{{ accountInfo.username || "未设置" }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">昵称</span>
          <span class="info-value">{{ accountInfo.nickname || "未设置" }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">邮箱</span>
          <span class="info-value">{{ accountInfo.email || "未绑定" }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">手机号码</span>
          <span class="info-value">{{ accountInfo.phone || "未绑定" }}</span>
        </div>
        <div v-if="accountInfo.registerDate" class="info-row">
          <span class="info-label">注册时间</span>
          <span class="info-value">{{ accountInfo.registerDate }}</span>
        </div>
      </div>
    </div>

    <div class="account-section">
      <h3 class="section-title">账号操作</h3>
      <div class="action-buttons">
        <VBtn variant="outlined" size="small" class="action-btn" @click="handleChangePassword">
          修改密码
        </VBtn>
        <VBtn variant="outlined" size="small" class="action-btn" @click="handleBindEmail">
          绑定邮箱
        </VBtn>
        <VBtn
          variant="outlined"
          color="error"
          size="small"
          class="action-btn"
          @click="handleDeleteAccount">
          注销账号
        </VBtn>
      </div>
    </div>

    <!-- 密码修改弹窗 -->
    <ChangePasswordDialog v-model="showPasswordDialog" />
  </div>
</template>

<style scoped>
.tab-panel {
  max-width: 100%;
}

.account-section {
  margin-bottom: 32px;
  padding: 0;
}

.account-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--app-border-light);
  transition: border-color 0.3s ease;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--app-text-primary);
  margin: 0;
  transition: color 0.3s ease;
}

.refresh-btn {
  text-transform: none;
  font-size: 12px;
}

.error-message {
  margin-bottom: 16px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  color: var(--app-text-secondary);
  font-size: 14px;
  transition: color 0.3s ease;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--app-border-light);
  transition: border-color 0.3s ease;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: var(--app-text-secondary);
  font-weight: 500;
  transition: color 0.3s ease;
}

.info-value {
  font-size: 14px;
  color: var(--app-text-primary);
  font-weight: 500;
  transition: color 0.3s ease;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 16px;
  justify-content: flex-start;
  align-items: center;
}

.action-btn {
  text-transform: none;
  font-size: 13px;
  font-weight: 500;
  border-radius: 4px;
  padding: 8px 16px;
  min-width: 80px;
  height: 36px;
  flex: none;
}
</style>
