<script setup>
import { ref, computed, onMounted } from "vue"
import { useChatStore } from "@/stores/baseStore"

// 导入子组件
import ImageUploadArea from "./components/ImageUploadArea.vue"
import MessageTextarea from "./components/MessageTextarea.vue"
import ActionButtons from "./components/ActionButtons.vue"
import ImagePreviewDialog from "./components/ImagePreviewDialog.vue"

// 导入 composables
import { useImageUpload } from "./composables/useImageUpload"
import { useAutoResize } from "./composables/useAutoResize"
import { useMessageSend } from "./composables/useMessageSend"

// 接收父组件传递的侧边栏状态
const props = defineProps({
  sidebarCollapsed: {
    type: Boolean,
    default: false,
  },
})

// 使用聊天store
const chatStore = useChatStore()

// 计算属性 - 监听照片墙和画布状态
const showPhotoWall = computed(() => chatStore.showPhotoWall)
const showImageCanvas = computed(() => chatStore.showImageCanvas)

// 响应式数据
const message = ref("")
const fileInputRef = ref(null)
const messageTextareaRef = ref(null)
const showPhotoArea = ref(false)

// 使用 composables
const {
  attachedImages,
  showImageDialog,
  selectedImage,
  isUploading,
  uploadProgress,
  errorMessage,
  showError,
  addImageToAttachments,
  removeAttachedImage,
  clearAttachedImages,
  handleFileSelect,
  handlePaste,
  handleDragOver,
  handleDrop,
  openImagePreview,
  closeImagePreview,
  downloadImage,
  hideErrorMessage,
} = useImageUpload()

const { autoResize, resetTextareaHeight, scrollToBottomImmediate } =
  useAutoResize(messageTextareaRef)

const {
  deepThinkActive,
  webSearchActive,
  isStreaming,
  handleSend,
  handleDeepThink,
  handleWebSearch,
  handleKeydown,
  stopStreaming,
} = useMessageSend()

// 处理发送消息
const handleSendMessage = async () => {
  await handleSend(
    message.value,
    attachedImages.value,
    clearAttachedImages,
    resetTextareaHeight,
    scrollToBottomImmediate
  )
  // 清空输入框
  message.value = ""
}

// 处理键盘事件
const handleTextareaKeydown = (event) => {
  handleKeydown(event, handleSendMessage)
}

// 处理附件点击
const handleAttachment = () => {
  fileInputRef.value?.click()
}

// 处理文件选择后清空input
const handleFileSelectAndClear = (event) => {
  handleFileSelect(event)
  // 清空input
  if (fileInputRef.value) {
    fileInputRef.value.value = ""
  }
}

// 切换照片区域显示
const togglePhotoArea = () => {
  showPhotoArea.value = !showPhotoArea.value
}

// 计算发送按钮是否禁用
const isSendDisabled = computed(() => {
  // 如果没有消息内容且没有图片，禁用发送
  if (!message.value.trim() && attachedImages.value.length === 0) {
    return true
  }

  // 如果正在加载中，禁用发送
  if (chatStore.isLoading) {
    return true
  }

  // 如果有图片正在上传，禁用发送
  const hasUploadingImages = attachedImages.value.some((img) => img.uploading)
  if (hasUploadingImages) {
    return true
  }

  // 如果有图片上传失败，禁用发送
  const hasFailedImages = attachedImages.value.some((img) => img.uploadError)
  if (hasFailedImages) {
    return true
  }

  return false
})

// 计算输入框占位符
const inputPlaceholder = computed(() => {
  // 检查是否有图片正在上传
  const hasUploadingImages = attachedImages.value.some((img) => img.uploading)
  if (hasUploadingImages) {
    return "图片上传中，请稍候..."
  }

  // 检查是否有图片上传失败
  const hasFailedImages = attachedImages.value.some((img) => img.uploadError)
  if (hasFailedImages) {
    return "图片上传失败，请重新上传..."
  }

  // 正常状态
  return attachedImages.value.length > 0 ? "添加描述（可选）..." : "给Dolphin AI 发送消息..."
})

// 组件挂载时初始化textarea高度
onMounted(() => {
  autoResize()
})
</script>

<template>
  <div
    class="chat-input-container"
    :class="{
      'sidebar-collapsed': sidebarCollapsed,
      'with-photo-wall': showPhotoWall,
      'with-image-canvas': showImageCanvas,
    }">
    <VContainer>
      <div class="input-wrapper">
        <div class="deepseek-input-card">
          <!-- 主要内容区域 - 左右布局 -->
          <div class="main-content-layout">
            <!-- 左侧照片区域 -->
            <div v-if="showPhotoArea || attachedImages.length > 0" class="left-photo-section">
              <div class="photo-grid">
                <div
                  v-for="image in attachedImages"
                  :key="image.id"
                  class="photo-item"
                  @click="openImagePreview(image)">
                  <img :src="image.url" :alt="image.name" class="photo-thumbnail" />
                  <VBtn
                    icon="mdi-close"
                    size="x-small"
                    variant="text"
                    class="photo-remove-btn"
                    @click.stop="removeAttachedImage(image.id)" />

                  <!-- 上传进度 -->
                  <div v-if="image.uploading" class="photo-uploading">
                    <VProgressCircular
                      :model-value="uploadProgress"
                      size="20"
                      width="2"
                      color="primary" />
                  </div>
                </div>

                <!-- 添加更多照片按钮 -->
                <div
                  v-if="attachedImages.length < 4"
                  class="add-photo-btn"
                  @click="handleAttachment">
                  <VIcon size="20">mdi-plus</VIcon>
                </div>
              </div>
            </div>

            <!-- 右侧输入区域 -->
            <div class="right-input-section">
              <!-- 消息输入框区域 -->
              <MessageTextarea
                ref="messageTextareaRef"
                v-model="message"
                :placeholder="inputPlaceholder"
                @keydown="handleTextareaKeydown"
                @input="autoResize"
                @paste="handlePaste"
                @dragover="handleDragOver"
                @drop="handleDrop" />

              <!-- 底部按钮行 -->
              <ActionButtons
                :deep-think-active="deepThinkActive"
                :web-search-active="webSearchActive"
                :show-photo-area="showPhotoArea"
                :has-images="attachedImages.length > 0"
                :disabled="isSendDisabled"
                :is-loading="chatStore.isLoading"
                :is-streaming="isStreaming"
                @deep-think="handleDeepThink"
                @web-search="handleWebSearch"
                @toggle-photo="togglePhotoArea"
                @attachment="handleAttachment"
                @send="handleSendMessage"
                @stop="stopStreaming" />
            </div>
          </div>
        </div>
      </div>
    </VContainer>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      accept="image/*"
      multiple
      style="display: none"
      @change="handleFileSelectAndClear" />

    <!-- 图片预览对话框 -->
    <ImagePreviewDialog
      :show="showImageDialog"
      :selected-image="selectedImage"
      @close="closeImagePreview"
      @download="downloadImage" />

    <!-- 错误提示 Snackbar -->
    <VSnackbar
      v-model="showError"
      :timeout="3000"
      color="error"
      location="top"
      @click="hideErrorMessage">
      <VIcon icon="mdi-alert-circle" class="me-2" />
      {{ errorMessage }}
      <template #actions>
        <VBtn icon="mdi-close" size="small" variant="text" @click="hideErrorMessage" />
      </template>
    </VSnackbar>
  </div>
</template>

<style scoped>
.chat-input-container {
  position: fixed;
  bottom: 0;
  left: 280px; /* 侧边栏展开时的宽度 */
  right: 0;
  /* background-color: var(--app-bg-secondary); */
  padding: 8px 0;
  z-index: 990;
  transition: left 0.3s ease, right 0.3s ease, background-color 0.3s ease;
}

/* 侧边栏收缩时的样式 */
.chat-input-container.sidebar-collapsed {
  left: 60px; /* 侧边栏收缩时的宽度 */
}

/* 照片墙打开时的样式 */
.chat-input-container.with-photo-wall {
  right: 320px; /* 照片墙的宽度 */
}

/* 画布打开时的样式 */
.chat-input-container.with-image-canvas {
  right: 500px; /* 画布的宽度 */
}

/* 同时打开照片墙和画布时的样式 */
.chat-input-container.with-photo-wall.with-image-canvas {
  right: 820px; /* 320px + 500px */
}

/* 同时有侧边栏收缩和照片墙打开的样式 */
.chat-input-container.sidebar-collapsed.with-photo-wall {
  left: 60px;
  right: 320px;
}

.input-wrapper {
  max-width: 800px;
  margin: 0 auto;
}

.deepseek-input-card {
  border: 1px solid rgba(102, 126, 234, 0.15);
  border-radius: 28px;
  background: var(--app-bg-primary);
  overflow: hidden;
  transition: border-color 0.2s ease, background-color 0.3s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 8px var(--app-shadow-light);
}

/* 主要内容布局 - 左右结构 */
.main-content-layout {
  display: flex;
  min-height: 60px;
}

/* 左侧照片区域 */
.left-photo-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 12px 8px 12px 20px;
  border-right: 1px solid rgba(102, 126, 234, 0.1);
  min-width: 120px;
  max-width: 200px;
}

/* 照片网格布局 - 一行两个 */
.photo-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  width: 100%;
}

/* 右侧输入区域 */
.right-input-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 照片项目 */
.photo-item {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.2s ease;
}

.photo-item:hover {
  border-color: rgba(102, 126, 234, 0.3);
}

.photo-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 照片删除按钮 */
.photo-remove-btn {
  position: absolute;
  top: -4px;
  right: -4px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  min-width: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

/* 照片上传进度 */
.photo-uploading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  padding: 4px;
}

/* 添加照片按钮 */
.add-photo-btn {
  width: 50px;
  height: 50px;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: rgba(102, 126, 234, 0.6);
}

.add-photo-btn:hover {
  border-color: rgba(102, 126, 234, 0.6);
  color: rgba(102, 126, 234, 0.8);
}

.deepseek-input-card:hover {
  border-color: rgba(102, 126, 234, 0.25);
}

.deepseek-input-card:focus-within {
  border-color: rgba(102, 126, 234, 0.4);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

/* 深色主题适配 */
[data-theme="dark"] .deepseek-input-card {
  border-color: rgba(255, 255, 255, 0.15);
  background: #000000 !important;
  background-color: #000000 !important;
}

[data-theme="dark"] .deepseek-input-card:hover {
  border-color: rgba(255, 255, 255, 0.25);
  background: #000000 !important;
  background-color: #000000 !important;
}

[data-theme="dark"] .deepseek-input-card:focus-within {
  border-color: rgba(33, 150, 243, 0.6);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
  background: #000000 !important;
  background-color: #000000 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-input-container {
    left: 0;
    right: 0;
    padding: 6px 0;
  }

  /* 移动端照片墙打开时不改变输入框位置 */
  .chat-input-container.with-photo-wall {
    right: 0;
  }

  /* 移动端照片区域调整 */
  .left-photo-section {
    padding: 8px 6px 8px 12px;
    min-width: 80px;
    max-width: 120px;
  }

  .photo-grid {
    gap: 6px;
  }

  .photo-item,
  .add-photo-btn {
    width: 35px;
    height: 35px;
  }

  .photo-remove-btn {
    min-width: 12px !important;
    width: 12px !important;
    height: 12px !important;
    top: -2px;
    right: -2px;
  }
}
</style>
