<template>
  <div class="text-content">
    <!-- 编辑模式 -->
    <div v-if="isEditing" class="edit-mode">
      <textarea
        ref="editTextareaRef"
        v-model="editingContent"
        class="edit-textarea"
        rows="3"
        @keydown="handleKeydown"
        @input="autoResize"
        placeholder="编辑消息内容..."></textarea>

      <div class="edit-actions">
        <VBtn
          size="small"
          variant="outlined"
          @click="handleCancel">
          取消
        </VBtn>
        <VBtn
          size="small"
          color="primary"
          @click="handleSend"
          :disabled="!editingContent.trim()">
          发送
        </VBtn>
      </div>
    </div>

    <!-- 正常显示模式 -->
    <div v-else>
      <!-- 文本内容 -->
      <div v-if="content" class="text-rendered" v-html="htmlContent"></div>

      <!-- 图片内容 -->
      <div v-if="hasImages" class="message-images">
        <div v-for="(imageData, index) in imageUrls" :key="imageData.path || index" class="image-item">
          <img
            v-if="imageData.url"
            :src="imageData.url"
            :alt="`图片 ${index + 1}`"
            class="message-image"
            @click="openImagePreview(imageData, index)"
            @load="handleImageLoad(imageData)"
            @error="handleImageError(imageData)" />

          <!-- 图片加载失败显示 -->
          <div v-else class="image-error">
            <VIcon icon="mdi-image-broken" size="24" />
            <span class="error-text">图片加载失败</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览弹窗 -->
    <VDialog v-model="showImagePreview" max-width="90vw" max-height="90vh" class="image-preview-dialog">
      <VCard class="image-preview-card">
        <VCardTitle class="preview-header">
          <span>图片预览 ({{ currentImageIndex + 1 }}/{{ imageUrls.length }})</span>
          <VBtn
            icon="mdi-close"
            variant="text"
            size="small"
            @click="closeImagePreview" />
        </VCardTitle>

        <VCardText class="preview-content">
          <div class="preview-image-container">
            <img
              v-if="currentPreviewImage?.url"
              :src="currentPreviewImage.url"
              :alt="`预览图片 ${currentImageIndex + 1}`"
              class="preview-image" />
          </div>

          <!-- 预览导航 -->
          <div v-if="imageUrls.length > 1" class="preview-navigation">
            <VBtn
              icon="mdi-chevron-left"
              variant="text"
              :disabled="currentImageIndex === 0"
              @click="previousImage" />

            <span class="nav-info">{{ currentImageIndex + 1 }} / {{ imageUrls.length }}</span>

            <VBtn
              icon="mdi-chevron-right"
              variant="text"
              :disabled="currentImageIndex === imageUrls.length - 1"
              @click="nextImage" />
          </div>
        </VCardText>
      </VCard>
    </VDialog>
  </div>
</template>

<script setup>
import { computed, ref, watch, nextTick, onUnmounted } from "vue"
import { renderMarkdown } from "@/utils/markdown"
import { useChatStore } from "@/stores/baseStore"

// 接收props
const props = defineProps({
  content: {
    type: String,
    required: true,
  },
  isEditing: {
    type: Boolean,
    default: false,
  },
  // 添加消息数据prop用于图片处理
  message: {
    type: Object,
    default: () => ({})
  }
})

// 定义emits
const emit = defineEmits(['finish-editing', 'cancel-editing'])

// 使用store
const chatStore = useChatStore()

// 响应式数据
const editTextareaRef = ref(null)
const editingContent = ref('')

// 图片预览相关
const showImagePreview = ref(false)
const currentImageIndex = ref(0)

// 计算属性 - Markdown 渲染
const htmlContent = computed(() => {
  if (!props.content) return ""
  // 使用 markdown-it 进行渲染
  return renderMarkdown(props.content)
})

// 图片相关计算属性
const hasImages = computed(() => {
  const result = props.message && props.message.images && props.message.images.length > 0
  return result
})

const imageUrls = computed(() => {
  if (!props.message || !props.message.imageUrls) {
    return []
  }
  return props.message.imageUrls.filter(img => img && img.url)
})

const currentPreviewImage = computed(() => {
  return imageUrls.value[currentImageIndex.value] || null
})

// 监听编辑状态变化
watch(() => props.isEditing, (newVal) => {
  if (newVal) {
    // 进入编辑模式时，初始化编辑内容
    editingContent.value = props.content
    nextTick(() => {
      // 聚焦到输入框并自动调整高度
      if (editTextareaRef.value) {
        editTextareaRef.value.focus()
        autoResize()
      }
    })
  }
})

// 自动调整textarea高度
const autoResize = () => {
  if (editTextareaRef.value) {
    editTextareaRef.value.style.height = 'auto'
    editTextareaRef.value.style.height = editTextareaRef.value.scrollHeight + 'px'
  }
}

// 处理键盘事件
const handleKeydown = (event) => {
  // Ctrl+Enter 或 Cmd+Enter 发送
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault()
    handleSend()
  }
  // Escape 取消编辑
  if (event.key === 'Escape') {
    event.preventDefault()
    handleCancel()
  }
}

// 处理发送
const handleSend = () => {
  if (editingContent.value.trim()) {
    emit('finish-editing', editingContent.value.trim())
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel-editing')
}

// 图片处理方法
const openImagePreview = (imageData, index) => {
  currentImageIndex.value = index
  showImagePreview.value = true
}

const closeImagePreview = () => {
  showImagePreview.value = false
}

const previousImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--
  }
}

const nextImage = () => {
  if (currentImageIndex.value < imageUrls.value.length - 1) {
    currentImageIndex.value++
  }
}

const handleImageLoad = (imageData) => {
  console.log('图片加载成功:', imageData.path)
}

const handleImageError = (imageData) => {
  console.error('图片加载失败:', imageData.path)
}

// 图片预览键盘事件处理
const handleImagePreviewKeydown = (event) => {
  if (!showImagePreview.value) return

  switch (event.key) {
    case 'Escape':
      closeImagePreview()
      break
    case 'ArrowLeft':
      previousImage()
      break
    case 'ArrowRight':
      nextImage()
      break
  }
}

// 监听预览弹窗状态
watch(showImagePreview, (isOpen) => {
  if (isOpen) {
    document.addEventListener('keydown', handleImagePreviewKeydown)
  } else {
    document.removeEventListener('keydown', handleImagePreviewKeydown)
  }
})

// 组件卸载时清理
onUnmounted(() => {
  document.removeEventListener('keydown', handleImagePreviewKeydown)
})
</script>

<style lang="css" scoped>
.text-content {
  font-size: 15px;
  color: #1f2937;
  line-height: 1.6;
  font-weight: 400;
}

/* 编辑模式样式 */
.edit-mode {
  width: 100%;
  min-width: 400px; /* 设置最小宽度 */
}

.edit-textarea {
  width: 100%;
  min-width: 400px; /* 输入框最小宽度 */
  min-height: 80px; /* 增加最小高度 */
  max-height: 300px; /* 增加最大高度 */
  padding: 16px; /* 增加内边距 */
  border: 1px solid #e5e7eb;
  border-radius: 12px; /* 增加圆角 */
  font-size: 15px;
  line-height: 1.6;
  font-family: inherit;
  resize: none;
  outline: none;
  background: #ffffff;
  color: #1f2937;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 添加阴影 */
}

.edit-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px); /* 轻微上移效果 */
}

.edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 12px;
}

.edit-actions .v-btn {
  min-width: 60px;
  height: 32px;
}

/* 响应式设计 - 编辑模式 */
@media (max-width: 768px) {
  .edit-mode {
    min-width: 300px;
  }

  .edit-textarea {
    min-width: 300px;
    padding: 12px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .edit-mode {
    min-width: 280px;
  }

  .edit-textarea {
    min-width: 280px;
    padding: 10px;
    font-size: 15px;
  }

  .edit-actions {
    gap: 6px;
  }

  .edit-actions .v-btn {
    min-width: 50px;
    height: 30px;
    font-size: 14px;
  }
}

/* Markdown 元素样式 */

.text-rendered :deep(h1),
.text-rendered :deep(h2),
.text-rendered :deep(h3),
.text-rendered :deep(h4),
.text-rendered :deep(h5),
.text-rendered :deep(h6) {
  margin: 6px 0 2px 0;
  font-weight: 600;
  line-height: 1.2;
  color: #1f2937;
}

.text-rendered :deep(h1) {
  font-size: 28px;
}
.text-rendered :deep(h2) {
  font-size: 24px;
}
.text-rendered :deep(h3) {
  font-size: 20px;
}
.text-rendered :deep(h4) {
  font-size: 18px;
}
.text-rendered :deep(h5) {
  font-size: 16px;
}
.text-rendered :deep(h6) {
  font-size: 15px;
}

.text-rendered :deep(p) {
  margin: 2px 0;
  line-height: 1.4;
}

.text-rendered :deep(ul),
.text-rendered :deep(ol) {
  margin: 2px 0;
  padding-left: 16px;
}

.text-rendered :deep(li) {
  margin: 1px 0;
  line-height: 1.3;
}

.text-rendered :deep(blockquote) {
  margin: 6px 0;
  padding: 6px 12px;
  border-left: 3px solid #e5e7eb;
  background-color: #f9fafb;
  color: #6b7280;
  font-style: italic;
}

.text-rendered :deep(code) {
  background-color: #f3f4f6;
  color: #e11d48;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: "Consolas", "Monaco", "Courier New", monospace;
  font-size: 13px;
}

.text-rendered :deep(pre) {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 6px 0;
  font-family: "Consolas", "Monaco", "Courier New", monospace;
  font-size: 13px;
  line-height: 1.3;
}

.text-rendered :deep(pre code) {
  background: none;
  color: inherit;
  padding: 0;
  border-radius: 0;
}

.text-rendered :deep(table) {
  border-collapse: collapse;
  margin: 6px 0;
  width: 100%;
}

.text-rendered :deep(th),
.text-rendered :deep(td) {
  border: 1px solid #e5e7eb;
  padding: 6px 10px;
  text-align: left;
}

.text-rendered :deep(th) {
  background-color: #f9fafb;
  font-weight: 600;
}

.text-rendered :deep(a) {
  color: #3b82f6;
  text-decoration: none;
}

.text-rendered :deep(a:hover) {
  text-decoration: underline;
}

.text-rendered :deep(strong) {
  font-weight: 600;
}

.text-rendered :deep(em) {
  font-style: italic;
}

.text-rendered :deep(hr) {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 8px 0;
}

/* 自定义容器样式 */
.text-rendered :deep(.custom-block) {
  margin: 6px 0;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid;
}

.text-rendered :deep(.custom-block-title) {
  font-weight: 600;
  margin: 0 0 4px 0;
}

.text-rendered :deep(.warning) {
  background-color: #fef3cd;
  border-left-color: #f59e0b;
  color: #92400e;
}

.text-rendered :deep(.tip) {
  background-color: #dbeafe;
  border-left-color: #3b82f6;
  color: #1e40af;
}

/* ===== 深色主题适配 ===== */
[data-theme="dark"] .text-content {
  color: var(--app-text-primary);
}

[data-theme="dark"] .text-rendered :deep(h1),
[data-theme="dark"] .text-rendered :deep(h2),
[data-theme="dark"] .text-rendered :deep(h3),
[data-theme="dark"] .text-rendered :deep(h4),
[data-theme="dark"] .text-rendered :deep(h5),
[data-theme="dark"] .text-rendered :deep(h6) {
  color: var(--app-text-primary);
}

[data-theme="dark"] .text-rendered :deep(blockquote) {
  background-color: rgba(45, 45, 45, 0.5);
  border-left: 3px solid rgba(255, 255, 255, 0.3);
  color: var(--app-text-secondary);
}

[data-theme="dark"] .text-rendered :deep(code) {
  background-color: rgba(45, 45, 45, 0.8);
  color: #ff6b6b;
}

[data-theme="dark"] .text-rendered :deep(th),
[data-theme="dark"] .text-rendered :deep(td) {
  border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .text-rendered :deep(th) {
  background-color: rgba(45, 45, 45, 0.8);
}

[data-theme="dark"] .text-rendered :deep(strong) {
  color: var(--app-text-primary);
}

[data-theme="dark"] .text-rendered :deep(hr) {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .text-rendered :deep(.warning) {
  background-color: rgba(245, 158, 11, 0.1);
  border-left-color: #f59e0b;
  color: #fbbf24;
}

[data-theme="dark"] .text-rendered :deep(.tip) {
  background-color: rgba(59, 130, 246, 0.1);
  border-left-color: #3b82f6;
  color: #60a5fa;
}

/* 深色主题编辑模式样式 */
[data-theme="dark"] .edit-textarea {
  background: var(--app-bg-secondary);
  border: 1px solid var(--app-border-color);
  color: var(--app-text-primary);
}

[data-theme="dark"] .edit-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* ===== 图片样式 ===== */
.message-images {
  margin: 8px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--app-bg-secondary);
  width: 200px;
  height: 150px;
}

.message-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s ease;
  display: block;
}

.message-image:hover {
  transform: scale(1.02);
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: var(--app-text-secondary);
  background-color: var(--app-bg-secondary);
  min-height: 80px;
  border-radius: 8px;
}

.error-text {
  margin-top: 8px;
  font-size: 12px;
}

/* 深色主题下的图片样式 */
[data-theme="dark"] .image-error {
  background-color: rgba(45, 45, 45, 0.5);
  color: rgba(255, 255, 255, 0.6);
}

/* ===== 图片预览弹窗样式 ===== */
.image-preview-dialog :deep(.v-overlay__content) {
  margin: 0;
  max-width: 90vw;
  max-height: 90vh;
}

.image-preview-card {
  background-color: var(--app-bg-primary);
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--app-border-color);
  flex-shrink: 0;
}

.preview-content {
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.preview-image-container {
  width: 100%;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--app-bg-secondary);
  overflow: hidden;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: zoom-in;
}

.preview-navigation {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background-color: var(--app-bg-primary);
  border-top: 1px solid var(--app-border-color);
  width: 100%;
  justify-content: center;
  flex-shrink: 0;
}

.nav-info {
  font-size: 14px;
  color: var(--app-text-secondary);
  min-width: 60px;
  text-align: center;
}

/* 深色主题下的预览弹窗样式 */
[data-theme="dark"] .image-preview-card {
  background-color: var(--app-bg-primary);
}

[data-theme="dark"] .preview-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .preview-image-container {
  background-color: rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .preview-navigation {
  background-color: var(--app-bg-primary);
  border-top-color: rgba(255, 255, 255, 0.1);
}
</style>
