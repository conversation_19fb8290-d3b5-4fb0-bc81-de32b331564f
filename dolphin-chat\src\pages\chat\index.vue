<script setup>
import { computed, ref, nextTick, watch, onMounted, onUnmounted, inject } from "vue"
import { useChatStore } from "@/stores/baseStore"
import ChatMessage from "./components/chatmessage.vue"
import PhotoWall from "./components/PhotoWall.vue"
import ImageCanvas from "./components/ImageCanvas.vue"
import AdaptiveTopBar from "@/components/common/AdaptiveTopBar.vue"
import ComparisonTopBar from "@/components/common/ComparisonTopBar.vue"
import ComparisonModelSelector from "@/components/common/ComparisonModelSelector.vue"

// 使用聊天store
const chatStore = useChatStore()

// 注入侧边栏状态（如果可用）
const sidebarCollapsed = inject("sidebarCollapsed", ref(false))

// 清除确认对话框状态
const showClearDialog = ref(false)
const clearingConversation = ref(false)

// 三点菜单状态
const showChatMenu = ref(false)
const menuTrigger = ref(null)

// 响应式数据
const chatContainer = ref(null)

// 计算属性
const chatMessages = computed(() => chatStore.currentMessages)
const showPhotoWall = computed(() => chatStore.showPhotoWall)
const showImageCanvas = computed(() => chatStore.showImageCanvas)
const hasImages = computed(() => chatStore.imageMessages.length > 0)
// 对比模式相关计算属性
const comparisonMode = computed(() => chatStore.comparisonMode)
const comparisonMessages = computed(() => chatStore.currentComparisonMessages)
const leftModelInfo = computed(() => chatStore.leftModelInfo)
const rightModelInfo = computed(() => chatStore.rightModelInfo)

// 对比模式右侧的消息列表（显示对比模式开启后的新消息）
const rightSideMessages = computed(() => {
  if (!comparisonMode.value) {
    return []
  }

  // 获取右侧AI回复
  const aiMessages = comparisonMessages.value.filter(msg => msg.type === 'ai')
  // 如果有AI回复，显示对应的用户消息
  let recentUserMessages = []
  if (chatStore.comparisonStartTime) {
    const comparisonStartTime = new Date(chatStore.comparisonStartTime).getTime()
    recentUserMessages = chatMessages.value.filter(msg => {
      const msgTime = new Date(msg.timestamp).getTime()
      const isUserType = msg.type === 'user' || msg.type === 'image' || msg.type === 'multi-image'
      const isAfterStart = msgTime >= comparisonStartTime
      return isUserType && isAfterStart
    })
  } else if (aiMessages.length > 0) {
    // 如果没有开启时间但有AI回复，显示最新的用户消息
    const latestUserMessage = chatMessages.value
      .filter(msg => msg.type === 'user' || msg.type === 'image' || msg.type === 'multi-image')
      .slice(-1)
    recentUserMessages = latestUserMessage
 
  }

  // 合并并按时间戳排序
  const allMessages = [...recentUserMessages, ...aiMessages]
  const result = allMessages.sort((a, b) => {
    const timeA = new Date(a.timestamp).getTime()
    const timeB = new Date(b.timestamp).getTime()
    return timeA - timeB
  })
  return result
})

// 方法
const togglePhotoWall = () => {
  chatStore.togglePhotoWall()
  showChatMenu.value = false // 关闭菜单
}

// 切换三点菜单显示状态
const toggleChatMenu = () => {
  showChatMenu.value = !showChatMenu.value
}

// 处理共享对话 - 改为切换对比模式
const handleShareConversation = () => {
  // 切换对比模式（不需要检查消息长度）
  chatStore.toggleComparisonMode()
  showChatMenu.value = false

}

// 处理清除当前会话
const handleClearConversation = () => {
  if (chatMessages.value.length === 0) return
  showClearDialog.value = true
  showChatMenu.value = false // 关闭菜单
}

// 确认清除当前会话
const confirmClearConversation = async () => {
  if (clearingConversation.value) return

  clearingConversation.value = true

  try {
    const result = await chatStore.clearCurrentConversation()

    if (result.success) {
      showClearDialog.value = false
    
    } else {
      // 显示错误提示
      alert(result.message || "清空会话失败，请稍后重试")
    }
  } catch (error) {
    console.error("清空会话异常:", error)
    alert("清空会话失败，请稍后重试")
  } finally {
    clearingConversation.value = false
  }
}

// 取消清除操作
const cancelClearConversation = () => {
  showClearDialog.value = false
}

// 滚动到底部的方法 - 修复版本，支持对比模式
const scrollToBottom = (smooth = true) => {
  nextTick(() => {
    // 检查是否为对比模式
    if (comparisonMode.value) {
      // 对比模式：滚动左右两侧的消息容器
      const leftContainer = document.querySelector('.comparison-left .messages-container')
      const rightContainer = document.querySelector('.comparison-right .messages-container')

      // 滚动左侧容器
      if (leftContainer) {
        if (smooth) {
          leftContainer.scrollTo({
            top: leftContainer.scrollHeight,
            behavior: "smooth",
          })
        } else {
          leftContainer.scrollTop = leftContainer.scrollHeight
        }
      }

      // 滚动右侧容器
      if (rightContainer) {
        if (smooth) {
          rightContainer.scrollTo({
            top: rightContainer.scrollHeight,
            behavior: "smooth",
          })
        } else {
          rightContainer.scrollTop = rightContainer.scrollHeight
        }
      }
    } else {
      // 普通模式：使用聊天消息容器进行滚动
      const chatMessagesContainer = chatContainer.value || document.querySelector(".chat-messages")
      if (chatMessagesContainer) {
        if (smooth) {
          chatMessagesContainer.scrollTo({
            top: chatMessagesContainer.scrollHeight,
            behavior: "smooth",
          })
        } else {
          chatMessagesContainer.scrollTop = chatMessagesContainer.scrollHeight
        }
      }
    }
  })
}

const handleScrollToMessage = async (messageId) => {
  await nextTick()
  const messageElement = document.getElementById(`message-${messageId}`)
  if (messageElement && chatContainer.value) {
    messageElement.scrollIntoView({
      behavior: "smooth",
      block: "center",
    })
    // 添加高亮效果
    messageElement.classList.add("highlight")
    setTimeout(() => {
      messageElement.classList.remove("highlight")
    }, 2000)
  }
}

// 处理拖拽上传
const handleDragOver = (event) => {
  event.preventDefault()
  event.dataTransfer.dropEffect = "copy"
}

const handleDrop = (event) => {
  event.preventDefault()
  const files = event.dataTransfer.files
  if (files && files.length > 0) {
    const imageFiles = Array.from(files).filter((file) => file.type.startsWith("image/"))

    if (imageFiles.length === 0) return

    // 限制最多4张图片
    const maxImages = 4
    const filesToProcess = imageFiles.slice(0, maxImages)

    if (imageFiles.length > maxImages) {
      console.warn(`最多只能上传${maxImages}张图片，已为您选择前${maxImages}张`)
    }

    if (filesToProcess.length === 1) {
      // 单张图片使用原有逻辑
      const file = filesToProcess[0]
      const reader = new FileReader()
      reader.onload = (e) => {
        const imageData = {
          url: e.target.result,
          file: file,
          name: file.name,
          id: Date.now() + Math.random(),
        }
        chatStore.addImageMessage(imageData, "")
      }
      reader.readAsDataURL(file)
    } else {
      // 多张图片使用新的多图片逻辑
      const imagesData = []
      let loadedCount = 0

      filesToProcess.forEach((file, index) => {
        const reader = new FileReader()
        reader.onload = (e) => {
          imagesData[index] = {
            url: e.target.result,
            file: file,
            name: file.name,
            id: Date.now() + Math.random() + index,
          }
          loadedCount++

          // 当所有图片都加载完成后，创建多图片消息
          if (loadedCount === filesToProcess.length) {
            chatStore.addMultiImageMessage(imagesData, "")
          }
        }
        reader.readAsDataURL(file)
      })
    }
  }
}

// 监听消息变化，自动滚动到底部
watch(
  () => chatMessages.value.length,
  (newLength, oldLength) => {
    // 只有在消息数量增加时才滚动到底部
    if (newLength > oldLength) {
      // 使用nextTick确保DOM完全更新后再滚动
      nextTick(() => {
        scrollToBottom()
      })
    }
  }
)

// 监听加载状态变化，AI回复完成后滚动到底部
watch(
  () => chatStore.isLoading,
  (isLoading, wasLoading) => {
    // 当加载状态从true变为false时（AI回复完成），滚动到底部
    if (wasLoading && !isLoading) {
      // 使用nextTick确保DOM完全更新后再滚动
      nextTick(() => {
        scrollToBottom()
      })
    }
  }
)

// 监听消息数组的深度变化，确保任何消息更新都能触发滚动
watch(
  () => chatMessages.value,
  (newMessages, oldMessages) => {
    // 检查是否有新消息或消息内容发生变化
    if (newMessages && newMessages.length > 0) {
      const lastMessage = newMessages[newMessages.length - 1]
      const oldLastMessage =
        oldMessages && oldMessages.length > 0 ? oldMessages[oldMessages.length - 1] : null

      // 如果是新消息或最后一条消息的内容发生了变化，则滚动到底部
      if (
        !oldLastMessage ||
        lastMessage.id !== oldLastMessage.id ||
        lastMessage.content !== oldLastMessage.content
      ) {
        nextTick(() => {
          scrollToBottom()
        })
      }
    }
  },
  { deep: true }
)

// 监听对比消息的变化（右侧）
watch(
  () => comparisonMessages.value,
  (newMessages, oldMessages) => {
    // 只在对比模式下处理
    if (comparisonMode.value && newMessages && newMessages.length > 0) {
      const lastMessage = newMessages[newMessages.length - 1]
      const oldLastMessage =
        oldMessages && oldMessages.length > 0 ? oldMessages[oldMessages.length - 1] : null

      // 如果是新消息或最后一条消息的内容发生了变化，则滚动到底部
      if (
        !oldLastMessage ||
        lastMessage.id !== oldLastMessage.id ||
        lastMessage.content !== oldLastMessage.content
      ) {
        nextTick(() => {
          // 对比模式下，只滚动右侧容器
          const rightContainer = document.querySelector('.comparison-right .messages-container')
          if (rightContainer) {
            const scrollHeight = rightContainer.scrollHeight
            const clientHeight = rightContainer.clientHeight
            const maxScrollTop = scrollHeight - clientHeight
            rightContainer.scrollTo({
              top: maxScrollTop + 100, // 增加更多偏移量确保完全可见
              behavior: 'smooth',
            })
          }
        })
      }
    }
  },
  { deep: true }
)

// 监听对比模式的开启/关闭，开启时自动滚动到底部
watch(
  () => comparisonMode.value,
  (newMode, oldMode) => {
    // 当对比模式从关闭变为开启时，自动滚动到底部
    if (newMode && !oldMode) {
      // 使用 nextTick 确保 DOM 完全更新后再滚动
      nextTick(() => {
        // 延迟一点时间确保对比模式的 DOM 结构完全渲染
        setTimeout(() => {
          scrollToBottom(false) // 使用非平滑滚动，立即定位到底部
        }, 100)
      })
    }
  }
)

// 点击外部关闭菜单
const handleClickOutside = (event) => {
  // 排除照片墙和图片画布区域的点击
  const photoWallElement = document.querySelector(".photo-wall")
  const imageCanvasElement = document.querySelector(".image-canvas")

  if (photoWallElement && photoWallElement.contains(event.target)) {
    return // 不处理照片墙内的点击
  }

  if (imageCanvasElement && imageCanvasElement.contains(event.target)) {
    return // 不处理图片画布内的点击
  }

  if (showChatMenu.value && menuTrigger.value && !menuTrigger.value.$el.contains(event.target)) {
    const menuElement = document.querySelector(".chat-dropdown-menu")
    if (menuElement && !menuElement.contains(event.target)) {
      showChatMenu.value = false
    }
  }
}

// 组件挂载时，如果有消息则滚动到底部
onMounted(() => {
  if (chatMessages.value.length > 0) {
    // 使用非平滑滚动，立即定位到底部
    scrollToBottom(false)
  }
  // 添加全局点击事件监听
  document.addEventListener("click", handleClickOutside)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside)
})
</script>

<template>
  <div
    class="chat-page"
    :class="{ 'with-photo-wall': showPhotoWall, 'with-image-canvas': showImageCanvas }">
    <!-- 条件性顶部栏渲染 -->
    <!-- 普通模式：显示原有顶部栏 -->
    <AdaptiveTopBar
      v-if="!comparisonMode"
      :sidebar-collapsed="sidebarCollapsed"
      :show-image-canvas="showImageCanvas"
      @toggle-chat-menu="toggleChatMenu"
      @share-conversation="handleShareConversation"
      @toggle-photo-wall="togglePhotoWall"
      @clear-conversation="handleClearConversation" />

    <!-- 对比模式：显示专用顶部栏 -->
    <ComparisonTopBar v-if="comparisonMode" />

    <!-- 聊天消息区域 -->
    <div ref="chatContainer" class="chat-messages"
         :class="{ 'comparison-mode': comparisonMode }"
         @dragover="handleDragOver" @drop="handleDrop">

      <!-- 非对比模式：正常显示 -->
      <VContainer v-if="!comparisonMode">
        <div v-if="chatMessages.length === 0" class="empty-chat">
          <div class="empty-content">
            <VIcon icon="mdi-message-outline" size="64" color="grey-lighten-1" />
            <p class="empty-text">开始新的对话</p>
            <p class="empty-hint">您可以拖拽图片到这里添加图片</p>
          </div>
        </div>

        <!-- 显示聊天消息 -->
        <div v-for="message in chatMessages" :key="message.id" class="message-item">
          <ChatMessage :message="message" />
        </div>
      </VContainer>

      <!-- 对比模式：分屏显示 -->
      <div v-else class="comparison-layout">
        <!-- 左侧区域 -->
        <div class="comparison-left">
          <!-- 左侧消息区域 -->
          <div class="messages-container">
            <VContainer>
              <div v-if="chatMessages.length === 0" class="empty-chat">
                <div class="empty-content">
                  <VIcon icon="mdi-message-outline" size="48" color="grey-lighten-1" />
                  <p class="empty-text">开始新的对话</p>
                </div>
              </div>

              <!-- 显示左侧聊天消息 -->
              <div v-for="message in chatMessages" :key="message.id" class="message-item">
                <ChatMessage :message="message" />
              </div>
            </VContainer>
          </div>
        </div>

        <!-- 分隔线 -->
        <div class="comparison-divider"></div>

        <!-- 右侧区域 -->
        <div class="comparison-right">
          <!-- 右侧消息区域 -->
          <div class="messages-container">
            <VContainer>
              <div v-if="rightSideMessages.length === 0" class="empty-chat">
                <div class="empty-content">
                  <VIcon icon="mdi-compare" size="48" color="grey-lighten-1" />
                  <p class="empty-text">对比预览</p>
                </div>
              </div>

              <!-- 显示对比模式开启后的消息（用户消息 + 右侧AI回复） -->
              <div v-for="message in rightSideMessages" :key="message.id" class="message-item">
                <ChatMessage :message="message" />
              </div>
            </VContainer>
          </div>
        </div>
      </div>
    </div>

    <!-- 照片墙组件 -->
    <PhotoWall @scroll-to-message="handleScrollToMessage" />

    <!-- 图片画布组件 -->
    <ImageCanvas />

    <!-- 清除会话确认对话框 -->
    <VDialog v-model="showClearDialog" max-width="320" persistent>
      <VCard class="clear-confirm-dialog">
        <VCardTitle class="dialog-title">
          <VIcon icon="mdi-alert-circle" color="warning" class="me-2" />
          清空会话
        </VCardTitle>

        <VCardText class="dialog-content">
          <p class="confirm-text">确定要清空当前会话吗？</p>
        </VCardText>

        <VCardActions class="dialog-actions">
          <VSpacer />
          <VBtn
            variant="text"
            color="grey"
            @click="cancelClearConversation"
            :disabled="clearingConversation">
            取消
          </VBtn>
          <VBtn
            variant="flat"
            color="error"
            @click="confirmClearConversation"
            :loading="clearingConversation"
            :disabled="clearingConversation">
            {{ clearingConversation ? "清空中..." : "确定" }}
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>

<style scoped>
.chat-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: margin-right 0.3s ease;
  position: relative;
}

.chat-page.with-photo-wall {
  margin-right: 320px;
}

.chat-page.with-image-canvas {
  margin-right: 650px;
}

/* 当同时显示照片墙和画布时的处理 */
.chat-page.with-photo-wall.with-image-canvas {
  margin-right: 970px; /* 320px + 650px */
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 90px 0 100px 0; /* 顶部留出顶部栏空间，底部留出输入框空间 */
  position: relative;
  /* 确保滚动区域不会延伸到输入框下方 */
  height: 100%;
  max-height: 100%;
  z-index: 1;
}

/* 为聊天消息区域添加美化的滚动条 */
.chat-messages::-webkit-scrollbar {
  width: 10px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 8px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
  transform: scale(1.05);
}

.chat-messages::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, #4e5bc6 0%, #5e377e 100%);
  transform: scale(0.98);
}

.empty-chat {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 200px);
  text-align: center;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-text {
  font-size: 18px;
  color: var(--app-text-secondary);
  margin: 0;
  transition: color 0.3s ease;
}

.empty-hint {
  font-size: 14px;
  color: var(--app-text-tertiary);
  margin: 0;
  transition: color 0.3s ease;
}

.message-item {
  margin-bottom: 16px;
}

/* 消息高亮动画 */
.message-item.highlight {
  animation: highlight 2s ease-in-out;
}

@keyframes highlight {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(25, 118, 210, 0.1);
  }
  100% {
    background-color: transparent;
  }
}

/* 拖拽区域样式 */
.chat-messages[data-dragging="true"] {
  background-color: rgba(25, 118, 210, 0.05);
  border: 2px dashed #1976d2;
}

/* 对比模式样式 */
.chat-messages.comparison-mode {
  padding: 0;
  height: calc(100vh - 64px); /* 减去顶部栏高度 */
  overflow: hidden; /* 防止整体滚动，让各自区域滚动 */
}

.comparison-layout {
  display: flex;
  height: 100%;
  min-height: 0;
}

.comparison-left,
.comparison-right {
  flex: 1;
  width: 50%; /* 严格50%宽度 */
  display: flex;
  flex-direction: column;
  min-height: 0;
  min-width: 0; /* 防止内容溢出 */
}

.comparison-divider {
  width: 1px;
  background: var(--app-border-color);
  flex-shrink: 0;
}

/* 移除了模型选择器头部样式，现在在ComparisonTopBar中 */

.messages-container {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  height: 100%; /* 确保填充整个可用高度 */
}

.comparison-left .messages-container,
.comparison-right .messages-container {
  padding: 16px 0 120px 0; /* 增加底部padding，为输入框留出空间 */
  height: calc(100vh - 64px); /* 减去顶部栏高度 */
}

/* 对比模式下的空状态样式 */
.comparison-layout .empty-chat {
  padding: 40px 20px;
}

.comparison-layout .empty-content {
  text-align: center;
}

.comparison-layout .empty-text {
  font-size: 14px;
  color: rgba(var(--v-theme-on-surface), 0.6);
  margin-top: 12px;
}

/* 响应式设计 */
@media (max-width: 750px) {
  .chat-page.with-photo-wall {
    margin-right: 0;
  }

  .chat-page.with-image-canvas {
    margin-right: 0;
  }

  .chat-page.with-photo-wall.with-image-canvas {
    margin-right: 0;
  }

  .chat-messages {
    padding: 80px 0 90px 0; /* 移动端顶部留出顶部栏空间，底部留出输入框空间 */
  }

  /* 移动端滚动条更细 */
  .chat-messages::-webkit-scrollbar {
    width: 6px;
  }

  /* 移动端按钮位置调整 */
  .photo-wall-btn {
    top: 12px;
    right: 12px;
  }

  /* 移动端画布显示时，照片墙按钮隐藏或调整位置 */
  .photo-wall-btn.with-canvas {
    right: 12px; /* 移动端保持原位置，因为画布在移动端是全屏覆盖 */
    z-index: 1001; /* 提高层级确保可见 */
  }
}

@media (max-width: 480px) {
  /* 超小屏幕滚动条更细 */
  .chat-messages::-webkit-scrollbar {
    width: 4px;
  }
}

/* 清除会话确认对话框样式 */
.clear-confirm-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.clear-confirm-dialog .dialog-title {
  background: var(--app-bg-primary);
  padding: 16px 20px 12px;
  font-weight: 600;
  font-size: 16px;
  color: var(--app-text-primary);
  border-bottom: 1px solid var(--app-border-light);
  text-align: center;
}

.clear-confirm-dialog .dialog-content {
  padding: 16px 20px;
  background: var(--app-bg-primary);
  text-align: center;
}

.clear-confirm-dialog .confirm-text {
  font-size: 15px;
  color: var(--app-text-primary);
  margin-top: 10px;
  font-weight: 500;
}

.clear-confirm-dialog .warning-text {
  font-size: 13px;
  color: var(--app-text-secondary);
  margin: 0;
}

.clear-confirm-dialog .dialog-actions {
  padding: 12px 20px 16px;
  background: var(--app-bg-primary);
  gap: 12px;
}

/* ===== 深色主题适配 ===== */
[data-theme="dark"] .chat-page {
  background-color: var(--app-bg-secondary);
}

[data-theme="dark"] .chat-messages {
  background-color: var(--app-bg-secondary);
}

[data-theme="dark"] .empty-text {
  color: var(--app-text-secondary);
}

[data-theme="dark"] .empty-hint {
  color: var(--app-text-tertiary);
}

[data-theme="dark"] .photo-wall-btn {
  background: var(--app-bg-primary);
  border: 1px solid var(--app-border-color);
}

[data-theme="dark"] .photo-wall-btn:hover {
  background: var(--app-bg-tertiary);
}

[data-theme="dark"] .clear-conversation-btn {
  background: var(--app-bg-primary);
  border: 1px solid var(--app-border-color);
}

[data-theme="dark"] .clear-conversation-btn:hover {
  background: var(--app-bg-tertiary);
}
</style>
