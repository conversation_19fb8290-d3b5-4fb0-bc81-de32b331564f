<script setup>
import { ref, watch } from "vue"

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "错误提示",
  },
  message: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "error", // error, warning, info, success
    validator: (value) => ["error", "warning", "info", "success"].includes(value),
  },
  persistent: {
    type: Boolean,
    default: false,
  },
  showIcon: {
    type: Boolean,
    default: true,
  },
  confirmText: {
    type: String,
    default: "确定",
  },
})

// Emits
const emit = defineEmits(["update:modelValue", "confirm", "close"])

// 内部状态
const dialogVisible = ref(props.modelValue)

// 监听外部 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    dialogVisible.value = newVal
  }
)

// 监听内部状态变化，同步到外部
watch(dialogVisible, (newVal) => {
  emit("update:modelValue", newVal)
})

// 图标映射
const iconMap = {
  error: "mdi-alert-circle",
  warning: "mdi-alert",
  info: "mdi-information",
  success: "mdi-check-circle",
}

// 颜色映射
const colorMap = {
  error: "error",
  warning: "warning",
  info: "info",
  success: "success",
}

// 处理确认
const handleConfirm = () => {
  emit("confirm")
  dialogVisible.value = false
}

// 处理关闭
const handleClose = () => {
  if (!props.persistent) {
    emit("close")
    dialogVisible.value = false
  }
}

// 处理点击外部
const handleClickOutside = () => {
  if (!props.persistent) {
    handleClose()
  }
}

// 处理ESC键
const handleEscKey = () => {
  if (!props.persistent) {
    handleClose()
  }
}
</script>

<template>
  <!-- 纯 HTML 实现的错误弹窗 -->
  <Teleport to="body">
    <div
      v-if="dialogVisible"
      class="custom-overlay"
      @click="handleClickOutside"
      @keydown.esc="handleEscKey"
      tabindex="0">
      <div class="dialog-card" @click.stop>
        <!-- 头部区域 -->
        <div class="dialog-header">
          <div class="header-content">
            <VIcon
              v-if="showIcon"
              icon="mdi-close-circle"
              color="white"
              size="18"
              class="header-icon" />
            <h3 class="header-title">{{ title }}</h3>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="dialog-content">
          <div class="message-container">
            <p class="message-text">{{ message }}</p>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<style scoped>
/* 自定义遮罩层 */
.custom-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: overlay-fade-in 0.3s ease-out;
}

@keyframes overlay-fade-in {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(4px);
  }
}

/* 对话框卡片 */
.dialog-card {
  border-radius: 6px;
  overflow: hidden;
  background: #2d2d2d;
  box-shadow: none;
  border: none;
  animation: dialog-slide-in 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-width: 300px;
  width: 80vw;
}

@keyframes dialog-slide-in {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 头部样式 */
.dialog-header {
  padding: 14px 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background: #2d2d2d;
  position: relative;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.header-icon {
  flex-shrink: 0;
}

.header-title {
  font-size: 1rem;
  font-weight: 500;
  color: #ffffff;
  margin: 0;
  line-height: 1.3;
}

/* 内容区域 */
.dialog-content {
  padding: 0 16px 16px;
  background: #2d2d2d;
}

.message-container {
  text-align: left;
}

.message-text {
  font-size: 0.85rem;
  line-height: 1.3;
  color: #ffffff;
  margin: 0;
  word-break: break-word;
}

/* 深色主题适配 */
[data-theme="dark"] .custom-overlay {
  background: rgba(0, 0, 0, 0.85);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .dialog-card {
    margin: 16px;
    width: calc(100vw - 32px);
    max-width: 280px;
  }

  .dialog-header {
    padding: 14px 16px;
  }

  .dialog-content {
    padding: 12px 16px 16px;
  }

  .header-title {
    font-size: 0.95rem;
  }

  .message-text {
    font-size: 0.85rem;
  }
}

/* 隐藏默认的 Vuetify 对话框样式 */
.error-dialog :deep(.v-overlay__content) {
  margin: 0 !important;
  max-height: none !important;
  max-width: none !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.error-dialog :deep(.v-overlay__scrim) {
  display: none !important;
}
</style>
