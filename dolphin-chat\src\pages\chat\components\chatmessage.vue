<script setup>
import { computed, inject } from "vue"
import { useChatStore } from "@/stores/baseStore"

// 导入子组件
import MessageAvatar from "./ChatMessage/MessageAvatar.vue"
import MessageContent from "./ChatMessage/MessageContent.vue"
import MessageFooter from "./ChatMessage/MessageFooter.vue"
import ImagePreviewDialog from "./ChatMessage/ImagePreviewDialog.vue"

// 导入 composables
import { useImageHandling } from "./ChatMessage/composables/useImageHandling"

// 接收消息数据
const props = defineProps({
  message: {
    type: Object,
    required: true,
  },
})

// 使用store
const chatStore = useChatStore()

// 使用 composables
const { showImageDialog, openImagePreview, closeImagePreview, downloadImage } = useImageHandling()

// 计算属性
const isUser = computed(() => props.message.type === "user")
const isAI = computed(() => props.message.type === "ai")
const isImage = computed(
  () => props.message.type === "image" || props.message.type === "multi-image"
)

// 获取消息对应的模型名称
const selectedModelName = computed(() => {
  // 如果消息有模型ID，使用消息对应的模型
  if (props.message.modelId) {
    const messageModel = chatStore.availableModels.find(model => model.id === props.message.modelId)
    return messageModel?.name || "AI"
  }

  // 兼容旧消息，使用当前选中的模型
  const selectedModel = chatStore.getSelectedModel()
  return selectedModel?.name || "AI"
})

// 计算AI消息对应的用户询问图片
const userImages = computed(() => {
  if (!isAI.value) return []

  // 查找AI消息对应的用户询问
  const userMessage = chatStore.findUserMessageForAI(props.message.id)
  if (!userMessage) return []

  // 收集用户询问中的图片
  const images = []

  // 检查单张图片消息
  if (userMessage.type === "image" && userMessage.imageUrl) {
    images.push({
      url: userMessage.imageUrl,
      name: userMessage.imageName,
      id: userMessage.id,
    })
  }

  // 检查多张图片消息
  if (userMessage.type === "multi-image" && userMessage.images) {
    userMessage.images.forEach((img) => {
      images.push({
        url: img.url,
        name: img.name,
        id: img.id,
      })
    })
  }

  // 检查用户消息中的图片（包括type为user但包含图片的情况，主要用于历史记录）
  if (userMessage.type === "user" && userMessage.images && userMessage.images.length > 0) {
    // 处理从API加载的用户消息中的图片
    if (userMessage.imageUrls && userMessage.imageUrls.length > 0) {
      userMessage.imageUrls.forEach((img) => {
        images.push({
          url: img.url,
          name: img.path || `图片_${images.length + 1}`, // API返回的是path字段，如果没有则生成默认名称
          id: userMessage.id,
        })
      })
    }
  }



  return images
})

// 事件处理
const handleOpenImagePreview = () => {
  openImagePreview()
}

const handleCloseImagePreview = () => {
  closeImagePreview()
}

const handleDownloadImage = () => {
  downloadImage(props.message.imageUrl, props.message.imageName)
}

const handleOpenMultiImagePreview = (data) => {
  
}

// 新增：处理显示用户询问图片到画布
const handleShowUserImages = (images) => {
  // 将图片添加到画布并显示
  chatStore.addImagesToCanvas(images)
}

// 新增：处理重新生成消息
const handleRegenerateMessage = () => {
  chatStore.regenerateMessage(props.message.id)
}

// 处理编辑消息
const handleEditMessage = () => {
  chatStore.startEditingMessage(props.message.id)
}

// 处理完成编辑
const handleFinishEditing = async (newContent) => {
  const result = await chatStore.finishEditingMessage(newContent)
  if (result.success && result.updatedMessage) {
    // 编辑成功后，重新发送消息获取AI回复
    await resendMessageForAI(result.updatedMessage)
  }
}

// 处理取消编辑
const handleCancelEditing = () => {
  chatStore.cancelEditingMessage()
}

// 重新发送消息获取AI回复
const resendMessageForAI = async (userMessage) => {
  try {
    chatStore.setLoading(true)

    // 准备请求数据
    const requestData = {
      question: userMessage.content,
      images: [],
      deepThinking: false,
      model: chatStore.selectedModel,
      sessionId: chatStore.currentConversationId,
    }

    // 创建新的AI消息用于流式更新
    const newAIMessage = chatStore.addAIMessage("", chatStore.selectedModel)
    chatStore.startStreamingMessage(newAIMessage.id)
    let accumulatedContent = ""

    // 动态导入ChatService
    const { ChatService } = await import("@/api/chat")

    // 调用流式API获取AI回复
    await ChatService.getAIReplyStream(
      requestData,
      (chunk) => {
        if (chunk) {
          accumulatedContent += chunk
          chatStore.updateAIMessage(newAIMessage.id, accumulatedContent)
        }
      },
      (error) => {
        console.error("❌ 编辑后重新获取AI回复错误:", error)
        chatStore.updateAIMessage(newAIMessage.id, "❌ 获取回复失败，请稍后再试。")
        chatStore.completeStreamingMessage(newAIMessage.id)
        chatStore.setLoading(false)
      },
      () => {
        chatStore.completeStreamingMessage(newAIMessage.id)
        chatStore.setLoading(false)
      }
    )
  } catch (error) {
    console.error("❌ 编辑后重新发送消息异常:", error)
    chatStore.setLoading(false)
  }
}
</script>

<template>
  <div
    :id="`message-${message.id}`"
    class="message-container"
    :class="{ 'user-message': isUser, 'ai-message': isAI, 'image-message': isImage }">
    <div class="message-wrapper">
      <!-- AI消息的头像和模型名称 -->
      <div v-if="isAI" class="ai-header">
        <MessageAvatar :message-type="message.type" />
        <div class="model-name">{{ selectedModelName }}</div>
      </div>

      <!-- 消息内容区域 -->
      <div class="message-content-wrapper" :class="{ 'ai-content': isAI }">
        <!-- 消息内容 -->
        <MessageContent
          :message="message"
          :message-type="message.type"
          :is-editing="chatStore.editingMessageId === message.id"
          @open-image-preview="handleOpenImagePreview"
          @open-multi-image-preview="handleOpenMultiImagePreview"
          @finish-editing="handleFinishEditing"
          @cancel-editing="handleCancelEditing" />

        <!-- 消息底部（操作按钮） -->
        <MessageFooter
          :message-type="message.type"
          :content="message.content"
          :image-url="message.imageUrl"
          :image-name="message.imageName"
          :user-images="userImages"
          @download-image="handleDownloadImage"
          @open-image-preview="handleOpenImagePreview"
          @show-user-images="handleShowUserImages"
          @regenerate-message="handleRegenerateMessage"
          @edit-message="handleEditMessage" />
      </div>

      <!-- 用户消息的头像 - 已隐藏 -->
      <!-- <MessageAvatar v-if="isUser" :message-type="message.type" /> -->
    </div>

    <!-- 图片预览对话框 -->
    <ImagePreviewDialog
      :show="showImageDialog"
      :image-url="message.imageUrl"
      :image-name="message.imageName"
      @close="handleCloseImagePreview"
      @download="handleDownloadImage" />
  </div>
</template>


<style scoped>
.message-container {
  margin-bottom: 8px;
  width: 100%;
  animation: fadeInUp 0.4s ease-out;
}

/* 最后一条消息增加额外的底部边距 */
.message-container:last-child {
  margin-bottom: -10px;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 100px;
  position: relative;
}

/* AI消息样式 */
.ai-message .message-wrapper {
  align-items: flex-start;
}

/* AI头像和模型名称的水平布局 */
.ai-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 模型名称样式 */
.model-name {
  font-size: 15px;
  font-weight: 550;
  color: var(--app-text-primary);
  line-height: 1.2;
}

/* 用户消息样式 */
.user-message .message-wrapper {
  align-items: flex-end;
  padding-right: 0; /* 去掉右侧内边距，因为没有头像了 */
}

/* 图片消息样式 */
.image-message .message-wrapper {
  align-items: flex-end;
}

/* 消息内容包装器 */
.message-content-wrapper {
  max-width: 92%;
  min-width: 120px;
  position: relative;
}

/* AI消息内容包装器 */
.ai-message .message-content-wrapper.ai-content {
  max-width: 100%;
  margin-left: 50px; /* 头像宽度 + 间距 */
}

/* 用户消息内容包装器 */
.user-message .message-content-wrapper {
  max-width: 92%;
  text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-wrapper {
    padding: 0 12px;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .message-wrapper {
    padding: 0 8px;
    gap: 10px;
  }
}
</style>
