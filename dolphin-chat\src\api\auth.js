/**
 * 认证相关API服务
 */

import request from '@/utils/request'
import { AUTH_API, USER_API } from './constants'

/**
 * 认证API服务类
 */
export class AuthService {
  /**
   * 用户登录
   * @param {Object} credentials 登录凭据
   * @param {string} credentials.username 用户名
   * @param {string} credentials.password 密码
   * @param {boolean} credentials.rememberMe 记住我
   * @returns {Promise} 登录结果
   */
  static async login(credentials) {
    try {
      const response = await request.post(AUTH_API.LOGIN, {
        username: credentials.username,
        password: credentials.password,
        rememberMe: credentials.rememberMe || false
      })



      // 检查响应格式
      if (response.code === 200 && response.data) {
        const { tokenName, accessToken, refreshToken } = response.data

        // 保存token信息
        if (accessToken) {
          localStorage.setItem('token', accessToken)
          localStorage.setItem('tokenName', tokenName || 'Bearer')
          if (refreshToken) {
            localStorage.setItem('refreshToken', refreshToken)
          }
          localStorage.setItem('isLoggedIn', 'true')

          // 创建用户信息对象（如果API没有返回用户信息，使用默认值）
          const userInfo = {
            username: credentials.username,
            name: credentials.username,
            // 可以根据需要添加更多字段
          }
          localStorage.setItem('user', JSON.stringify(userInfo))
        }

        return {
          success: true,
          data: {
            user: JSON.parse(localStorage.getItem('user')),
            token: accessToken,
            tokenName,
            refreshToken
          },
          message: response.msg || '登录成功'
        }
      } else {
        throw new Error(response.msg || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      return {
        success: false,
        error: error.message,
        message: error.message || '登录失败'
      }
    }
  }

  /**
   * 用户登出
   * @returns {Promise} 登出结果
   */
  static async logout() {
    try {
      await request.post(AUTH_API.LOGOUT)

      // 清除所有本地存储
      localStorage.clear()

      return {
        success: true,
        message: '登出成功'
      }
    } catch (error) {
      // 即使请求失败也要清除所有本地存储
      localStorage.clear()

      return {
        success: false,
        error: error.message,
        message: '登出失败'
      }
    }
  }

  /**
   * 用户注册
   * @param {Object} userData 注册数据
   * @param {string} userData.username 用户名
   * @param {string} userData.email 邮箱
   * @param {string} userData.password 密码
   * @param {string} userData.confirmPassword 确认密码
   * @returns {Promise} 注册结果
   */
  static async register(userData) {
    try {
      const response = await request.post(AUTH_API.REGISTER, userData)

      return {
        success: true,
        data: response,
        message: '注册成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '注册失败'
      }
    }
  }

  /**
   * 刷新token
   * @returns {Promise} 刷新结果
   */
  static async refreshToken() {
    try {
      const response = await request.post(AUTH_API.REFRESH_TOKEN)

      if (response.token) {
        localStorage.setItem('token', response.token)
      }

      return {
        success: true,
        data: response,
        message: 'Token刷新成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Token刷新失败'
      }
    }
  }

  /**
   * 忘记密码
   * @param {string} email 邮箱地址
   * @returns {Promise} 发送结果
   */
  static async forgotPassword(email) {
    try {
      const response = await request.post(AUTH_API.FORGOT_PASSWORD, { email })

      return {
        success: true,
        data: response,
        message: '重置密码邮件已发送'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '发送重置密码邮件失败'
      }
    }
  }

  /**
   * 重置密码
   * @param {Object} resetData 重置数据
   * @param {string} resetData.token 重置token
   * @param {string} resetData.password 新密码
   * @param {string} resetData.confirmPassword 确认密码
   * @returns {Promise} 重置结果
   */
  static async resetPassword(resetData) {
    try {
      const response = await request.post(AUTH_API.RESET_PASSWORD, resetData)

      return {
        success: true,
        data: response,
        message: '密码重置成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '密码重置失败'
      }
    }
  }

  /**
   * 验证邮箱
   * @param {string} token 验证token
   * @returns {Promise} 验证结果
   */
  static async verifyEmail(token) {
    try {
      const response = await request.post(AUTH_API.VERIFY_EMAIL, { token })

      return {
        success: true,
        data: response,
        message: '邮箱验证成功'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '邮箱验证失败'
      }
    }
  }

  /**
   * 发送验证码
   * @param {Object} codeData 验证码数据
   * @param {string} codeData.email 邮箱地址
   * @param {string} codeData.type 验证码类型 (register|login|reset)
   * @returns {Promise} 发送结果
   */
  static async sendVerificationCode(codeData) {
    try {
      const response = await request.post(AUTH_API.SEND_CODE, codeData)

      return {
        success: true,
        data: response,
        message: '验证码已发送'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: '验证码发送失败'
      }
    }
  }

  /**
   * 检查token有效性（本地检查）
   * @returns {boolean} token是否有效
   */
  static isTokenValid() {
    const token = localStorage.getItem('token')
    if (!token) return false

    try {
      // 简单的token格式检查（实际项目中可能需要更复杂的验证）
      const payload = JSON.parse(atob(token.split('.')[1]))
      const currentTime = Date.now() / 1000

      return payload.exp > currentTime
    } catch (error) {
      return false
    }
  }

  /**
   * 服务器端验证token有效性
   * 通过调用需要认证的API来验证token是否真正有效
   * @returns {Promise} 验证结果
   */
  static async validateTokenWithServer() {
    try {
      // 调用获取用户信息接口来验证token
      const response = await request.get(USER_API.GET_PROFILE, {
        headers: {
          'X-Silent-Request': 'true' // 静默请求，不显示错误提示
        }
      })

      // 检查响应状态码
      if (response.code === 200) {
        return {
          success: true,
          valid: true,
          data: response.data,
          message: 'Token验证成功'
        }
      } else if (response.code >= 4200 && response.code < 4300) {
        // 认证失败的状态码范围
        return {
          success: true,
          valid: false,
          code: response.code,
          message: response.msg || 'Token验证失败'
        }
      } else {
        return {
          success: true,
          valid: false,
          code: response.code,
          message: response.msg || 'Token验证失败'
        }
      }
    } catch (error) {
      console.error('服务器端token验证失败:', error)
      return {
        success: false,
        valid: false,
        error: error.message,
        message: '服务器端token验证失败'
      }
    }
  }

  /**
   * 获取当前用户信息
   * @returns {Object|null} 用户信息
   */
  static getCurrentUser() {
    try {
      const userStr = localStorage.getItem('user')
      return userStr ? JSON.parse(userStr) : null
    } catch (error) {
      return null
    }
  }

  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  static isLoggedIn() {
    return localStorage.getItem('isLoggedIn') === 'true' && this.isTokenValid()
  }
}

// 导出默认实例
export default AuthService
