<script setup>
// 定义props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: null
  },
  isEmpty: {
    type: Boolean,
    default: false
  }
})

// 定义emits
const emit = defineEmits(["retry"])

// 方法
const handleRetry = () => {
  emit("retry")
}
</script>

<template>
  <div class="conversation-states">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载会话列表中...</div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">⚠️</div>
      <div class="error-text">{{ error }}</div>
      <button class="retry-button" @click="handleRetry">重试</button>
    </div>

    <!-- 空状态 -->
    <div v-else-if="isEmpty" class="empty-container">
      <div class="empty-icon">💬</div>
      <div class="empty-text">暂无会话记录</div>
      <div class="empty-hint">开始新的对话吧</div>
    </div>
  </div>
</template>

<style scoped>
.conversation-states {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--app-text-secondary);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--app-border-color);
  border-top: 2px solid var(--v-theme-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 13px;
  color: var(--app-text-secondary);
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.error-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.error-text {
  font-size: 13px;
  color: var(--app-text-secondary);
  margin-bottom: 16px;
  line-height: 1.4;
}

.retry-button {
  padding: 6px 16px;
  background-color: var(--v-theme-primary);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retry-button:hover {
  background-color: var(--v-theme-primary-darken-1);
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 12px;
  opacity: 0.6;
}

.empty-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--app-text-primary);
  margin-bottom: 4px;
}

.empty-hint {
  font-size: 12px;
  color: var(--app-text-secondary);
  opacity: 0.8;
}
</style>
