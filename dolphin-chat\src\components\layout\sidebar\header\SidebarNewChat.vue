<script setup>
import { ref } from "vue"
import { useChatStore } from "@/stores/baseStore"
import { SessionService } from "@/api/session"
import icon32Url from "@/assets/icons/icon-32.svg"

// Props
const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false,
  },
})

// 定义emits
const emit = defineEmits(['session-created'])

// 使用聊天store
const chatStore = useChatStore()

// 响应式数据
const isCreating = ref(false)

// 方法
const createNewChat = async () => {
  if (isCreating.value) return

  try {
    isCreating.value = true

   

    // 必须先调用API创建新会话
    const result = await SessionService.createSession({
      name: '新对话',
      description: ''
    })

    if (result.success && result.data) {
      // 从API响应中提取会话ID
      let sessionId = null

      // 处理不同的响应格式
      if (result.data.id) {
        sessionId = result.data.id
      } else if (result.data.data && result.data.data.id) {
        sessionId = result.data.data.id
      } else if (typeof result.data === 'string') {
        sessionId = result.data
      }

      if (sessionId) {
        // 只有获取到API返回的ID才创建新对话
        chatStore.createNewConversation(sessionId)
       

        // 通知父组件刷新会话列表
        emit('session-created', sessionId)
      } else {
        console.error('❌ API返回的数据中未找到会话ID，无法创建会话')
        throw new Error('API返回的数据格式不正确，未找到会话ID')
      }
    } else {
      console.error('❌ 创建会话失败:', result.error || result.message)
      throw new Error(result.error || result.message || '创建会话失败')
    }
  } catch (error) {
    console.error('❌ 创建会话时发生错误:', error)
    // 可以在这里显示错误提示给用户
    // 不再创建本地会话，必须依赖API
  } finally {
    isCreating.value = false
  }
}
</script>

<template>
  <!-- 新对话按钮区域 -->
  <div class="new-chat-section">
    <!-- 展开状态的新对话按钮 -->
    <VBtn
      v-if="!isCollapsed"
      color="primary"
      variant="flat"
      size="default"
      class="new-chat-button"
      @click="createNewChat">
      <template #prepend>
        <img :src="icon32Url" alt="新对话" class="new-chat-icon" />
      </template>
      开启新对话
    </VBtn>

    <!-- 收缩状态下的新对话按钮 -->
    <VBtn
      v-if="isCollapsed"
      variant="text"
      size="default"
      class="collapsed-new-chat-btn new-chat-icon-btn"
      @click="createNewChat">
      <img :src="icon32Url" alt="新对话" class="new-chat-icon-small" />
    </VBtn>
  </div>
</template>

<style scoped>
.new-chat-section {
  width: 100%;
}

/* 收缩状态下的新对话区域 */
.sidebar-collapsed .new-chat-section {
  display: flex;
  justify-content: center;
  margin: 8px 0 4px 0;
}

.new-chat-button {
  width: 100%;
  height: 44px;
  text-transform: none;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  letter-spacing: 0.25px;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
  transition: all 0.2s ease;
  background-color: rgba(101, 167, 233, 0.58) !important;
}

.new-chat-button:hover {
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
  background-color: rgba(71, 156, 241, 0.88) !important;
}

.collapsed-new-chat-btn {
  width: 44px;
  height: 44px;
  min-width: 44px;
  border-radius: 8px;
  color: #888 !important;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.collapsed-new-chat-btn:hover {
  opacity: 0.9;
  background-color: rgba(0, 0, 0, 0.04);
  color: #666 !important;
}

/* 新对话图标样式 */
.new-chat-icon {
  width: 18px;
  height: 18px;
  object-fit: contain;
  margin-right: 8px;
  /* filter样式现在由主题CSS统一管理 */
}

.new-chat-icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0 auto;
}

.new-chat-icon-small {
  width: 18px;
  height: 18px;
  object-fit: contain;
  /* filter样式现在由主题CSS统一管理 */
}
</style>
