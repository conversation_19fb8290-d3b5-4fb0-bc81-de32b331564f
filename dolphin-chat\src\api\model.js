/**
 * 模型相关API服务
 */

import request from '@/utils/request'
import { MODEL_API } from './constants'

/**
 * 模型API服务类
 */
export class ModelService {
  /**
   * 获取模型列表
   * @returns {Promise} 模型列表数据
   */
  static async getModelList() {
    try {
      const response = await request.get(MODEL_API.GET_LIST)



      // 检查响应格式
      if (response.code === 200 && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.msg || '获取模型列表成功'
        }
      } else {
        throw new Error(response.msg || '获取模型列表失败')
      }
    } catch (error) {
      console.error('获取模型列表错误:', error)
      return {
        success: false,
        error: error.message,
        message: '获取模型列表失败',
        data: []
      }
    }
  }

  /**
   * 获取模型详情
   * @param {string} modelId 模型ID
   * @returns {Promise} 模型详情数据
   */
  static async getModelDetail(modelId) {
    try {
      const response = await request.get(`${MODEL_API.GET_DETAIL}/${modelId}`)



      if (response.code === 200 && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.msg || '获取模型详情成功'
        }
      } else {
        throw new Error(response.msg || '获取模型详情失败')
      }
    } catch (error) {
      console.error('获取模型详情错误:', error)
      return {
        success: false,
        error: error.message,
        message: '获取模型详情失败'
      }
    }
  }

  /**
   * 获取模型配置
   * @param {string} modelId 模型ID
   * @returns {Promise} 模型配置数据
   */
  static async getModelConfig(modelId) {
    try {
      const response = await request.get(`${MODEL_API.GET_CONFIG}/${modelId}`)



      if (response.code === 200 && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.msg || '获取模型配置成功'
        }
      } else {
        throw new Error(response.msg || '获取模型配置失败')
      }
    } catch (error) {
      console.error('获取模型配置错误:', error)
      return {
        success: false,
        error: error.message,
        message: '获取模型配置失败'
      }
    }
  }

  /**
   * 格式化模型数据
   * 将API返回的模型数据转换为组件需要的格式
   * @param {Array} rawModels API返回的原始模型数据
   * @returns {Array} 格式化后的模型数据
   */
  static formatModels(rawModels) {
    if (!Array.isArray(rawModels)) {
      console.warn('模型数据格式错误，期望数组类型:', rawModels)
      return []
    }

    return rawModels.map((model, index) => {
      // 处理字符串类型的模型数据
      let modelName, modelId, modelDesc

      if (typeof model === 'string') {
        // 如果是字符串，直接使用字符串作为名称和ID
        modelName = model
        modelId = model
        modelDesc = `${model} 模型`
      } else if (typeof model === 'object' && model !== null) {
        // 如果是对象，提取相应字段
        modelName = model.name || model.model_name || model.id || `模型${index + 1}`
        modelId = model.id || model.model_id || model.name || `model_${index}`
        modelDesc = model.description || model.desc || `${modelName} 模型`
      } else {
        // 其他情况的默认处理
        modelName = `模型${index + 1}`
        modelId = `model_${index}`
        modelDesc = `模型${index + 1}`
      }

      // 根据模型名称或类型设置图标和颜色
      const iconMap = {
        'gpt-4': { icon: 'mdi-brain', color: 'primary' },
        'gpt-3.5': { icon: 'mdi-lightning-bolt', color: 'success' },
        'claude': { icon: 'mdi-book-open-variant', color: 'info' },
        'gemini': { icon: 'mdi-star', color: 'warning' },
        'llama': { icon: 'mdi-llama', color: 'secondary' },
        'dolphin': { icon: 'mdi-dolphin', color: 'blue' },
        'ultrasound': { icon: 'mdi-waveform', color: 'teal' },
        'default': { icon: 'mdi-robot', color: 'grey' }
      }

      // 根据模型名称匹配图标
      let iconConfig = iconMap.default
      const lowerModelName = modelName.toLowerCase()

      for (const [key, config] of Object.entries(iconMap)) {
        if (lowerModelName.includes(key)) {
          iconConfig = config
          break
        }
      }

      return {
        id: modelId,
        name: modelName,
        description: modelDesc,
        icon: (typeof model === 'object' && model?.icon) || iconConfig.icon,
        color: (typeof model === 'object' && model?.color) || iconConfig.color,
        // 保留原始数据以备后用
        raw: model
      }
    })
  }
}

// 导出默认实例
export default ModelService
