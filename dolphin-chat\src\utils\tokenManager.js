/**
 * Token管理工具
 * 提供token有效性检查、自动刷新等功能
 */

import { AuthService } from '@/api/auth'

class TokenManager {
  constructor() {
    this.checkInterval = null
    this.refreshPromise = null
    this.isRefreshing = false
    this.failedQueue = []
  }

  /**
   * 启动token定时检查
   * @param {number} interval 检查间隔（毫秒），默认30秒
   */
  startTokenCheck(interval = 30000) {
    this.stopTokenCheck()
    
    this.checkInterval = setInterval(() => {
      this.checkTokenValidity()
    }, interval)
    
  }

  /**
   * 停止token定时检查
   */
  stopTokenCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
     
    }
  }

  /**
   * 检查token有效性
   * @returns {boolean} token是否有效
   */
  checkTokenValidity() {
    const token = localStorage.getItem('token')
    if (!token) {
      console.warn('🔒 Token不存在')
      return false
    }

    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      const currentTime = Date.now() / 1000
      const expirationTime = payload.exp
      const timeUntilExpiry = expirationTime - currentTime

      // 如果token在5分钟内过期，尝试刷新
      if (timeUntilExpiry <= 300 && timeUntilExpiry > 0) {
        console.warn('⚠️ Token即将过期，尝试刷新')
        this.refreshToken()
        return true
      }

      // 如果token已过期
      if (timeUntilExpiry <= 0) {
        console.warn('🔒 Token已过期')
        this.handleTokenExpired()
        return false
      }

      return true
    } catch (error) {
      console.error('❌ Token解析失败:', error)
      this.handleTokenExpired()
      return false
    }
  }

  /**
   * 刷新token
   * @returns {Promise} 刷新结果
   */
  async refreshToken() {
    // 如果正在刷新，返回现有的Promise
    if (this.isRefreshing) {
      return this.refreshPromise
    }

    this.isRefreshing = true
    this.refreshPromise = this._performTokenRefresh()

    try {
      const result = await this.refreshPromise
      this.processQueue(null, result)
      return result
    } catch (error) {
      this.processQueue(error, null)
      throw error
    } finally {
      this.isRefreshing = false
      this.refreshPromise = null
    }
  }

  /**
   * 执行token刷新
   * @private
   */
  async _performTokenRefresh() {
    try {
      const refreshToken = localStorage.getItem('refreshToken')
      if (!refreshToken) {
        throw new Error('RefreshToken不存在')
      }

      const result = await AuthService.refreshToken()
      if (result.success) {
        console.log('✅ Token刷新成功')
        return result
      } else {
        throw new Error(result.message || 'Token刷新失败')
      }
    } catch (error) {
      console.error('❌ Token刷新失败:', error)
      this.handleTokenExpired()
      throw error
    }
  }

  /**
   * 处理token过期
   */
  handleTokenExpired() {
    console.warn('🔒 Token已过期，执行登出操作')
    
    // 停止token检查
    this.stopTokenCheck()
    
    // 清除所有认证信息
    localStorage.clear()
    
    // 触发全局token过期事件
    window.dispatchEvent(new CustomEvent('token-expired', {
      detail: { message: 'Token已过期，请重新登录' }
    }))
    
    // 跳转到登录页面
    if (window.location.pathname !== '/auth/login') {
      window.location.href = '/auth/login'
    }
  }

  /**
   * 处理刷新队列
   * @private
   */
  processQueue(error, token = null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error)
      } else {
        resolve(token)
      }
    })
    
    this.failedQueue = []
  }

  /**
   * 添加到刷新队列
   * @param {Function} resolve Promise resolve函数
   * @param {Function} reject Promise reject函数
   */
  addToQueue(resolve, reject) {
    this.failedQueue.push({ resolve, reject })
  }

  /**
   * 获取token剩余时间（秒）
   * @returns {number} 剩余时间，-1表示无效token
   */
  getTokenRemainingTime() {
    const token = localStorage.getItem('token')
    if (!token) return -1

    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      const currentTime = Date.now() / 1000
      return Math.max(0, payload.exp - currentTime)
    } catch (error) {
      return -1
    }
  }

  /**
   * 格式化剩余时间显示
   * @returns {string} 格式化的时间字符串
   */
  getFormattedRemainingTime() {
    const seconds = this.getTokenRemainingTime()
    if (seconds <= 0) return '已过期'

    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = Math.floor(seconds % 60)

    if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    } else if (minutes > 0) {
      return `${minutes}分钟${remainingSeconds}秒`
    } else {
      return `${remainingSeconds}秒`
    }
  }
}

// 创建单例实例
const tokenManager = new TokenManager()

export default tokenManager
