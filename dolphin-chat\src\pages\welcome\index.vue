<script setup>
import { useChatStore } from "@/stores/baseStore"
import WelcomeSection from "./components/welcomesection.vue"
import FeatureCards from "./components/featurecards.vue"
import ModelSelector from "@/components/common/ModelSelector.vue"

// 使用聊天store
const chatStore = useChatStore()

// 方法
const handleStartChat = () => {
  chatStore.setShowWelcome(false)
}
</script>

<template>
  <div class="welcome-page">
    <!-- 左上角模型选择器 -->
    <div class="top-left-model-selector">
      <ModelSelector />
    </div>

    <WelcomeSection />
    <FeatureCards @feature-click="handleStartChat" />
  </div>
</template>

<style scoped>
.welcome-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 140px 24px 24px 24px;
  overflow-y: auto;
  position: relative;
}

.top-left-model-selector {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 100;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-page {
    padding: 80px 16px 16px 16px;
  }
}
</style>
