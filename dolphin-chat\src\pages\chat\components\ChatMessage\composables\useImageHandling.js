import { ref } from 'vue'

/**
 * 图片处理相关的 composable
 */
export function useImageHandling() {
  // 响应式数据
  const showImageDialog = ref(false)

  // 打开图片预览
  const openImagePreview = () => {
    showImageDialog.value = true
  }

  // 关闭图片预览
  const closeImagePreview = () => {
    showImageDialog.value = false
  }

  // 处理图片加载错误
  const handleImageError = (event) => {
    event.target.src = "/placeholder-image.png"
    console.warn("图片加载失败:", event.target.src)
  }

  // 下载图片
  const downloadImage = (imageUrl, imageName) => {
    if (imageUrl) {
      const link = document.createElement("a")
      link.href = imageUrl
      link.download = imageName || "image.png"
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  return {
    // 响应式数据
    showImageDialog,

    // 方法
    openImagePreview,
    closeImagePreview,
    handleImageError,
    downloadImage,
  }
}
