<template>
  <!-- 显示加载状态：当消息内容为空或正在加载时 -->
  <div v-if="isLoading" class="ai-loading-state">
    <div class="loading-content">
      <div class="loading-dots">
        <span class="dot"></span>
        <span class="dot"></span>
        <span class="dot"></span>
      </div>
      <span class="loading-text">Dolphin ai正在思考中</span>
    </div>
  </div>

  <!-- 正常消息内容 -->
  <div v-else-if="parsedMessage" class="ai-message-content">
    <!-- 思考过程标题（如果有思考内容） -->
    <div v-if="parsedMessage.hasThinking" class="think-header" @click="toggleThinkCollapse">
      <div class="think-title">
        <VIcon
          :icon="thinkCollapsed ? 'mdi-chevron-right' : 'mdi-chevron-down'"
          size="16"
          class="think-toggle-icon" />
        <span class="think-label">已深度思考</span>
        <span class="think-time">用时{{ parsedMessage.thinkingTime }}秒</span>
      </div>
    </div>

    <!-- 渲染消息内容 -->
    <div v-for="(part, index) in parsedMessage.parts" :key="index">
      <!-- 思考内容使用过渡动画 -->
      <Transition
        v-if="part.type === 'think'"
        name="think-collapse"
        @enter="onEnter"
        @leave="onLeave">
        <div
          v-show="!thinkCollapsed"
          :class="{
            'think-content': true,
            'think-content-streaming': part.complete === false,
          }"
          class="think-wrapper">
          <div class="text-content">
            {{ part.content }}
          </div>
          <!-- 为未完成的标签添加流式指示器 -->
          <span v-if="part.complete === false" class="streaming-indicator">▋</span>
        </div>
      </Transition>

      <!-- 非思考内容正常渲染 -->
      <div
        v-else
        :class="{
          'answer-content': part.type === 'answer',
          'normal-content': part.type === 'text',
          'markdown-content': part.type === 'answer' || part.type === 'text',
        }">
        <!-- 根据内容类型选择渲染方式 -->
        <div
          v-if="part.type === 'answer' || part.type === 'text'"
          v-html="part.htmlContent || part.content"
          class="markdown-rendered"></div>
        <div v-else class="text-content">
          {{ part.content }}
        </div>
        <!-- 为未完成的标签添加流式指示器 -->
        <span v-if="part.complete === false" class="streaming-indicator">▋</span>
      </div>
    </div>
  </div>

  <!-- 如果解析失败，显示原始内容 -->
  <div v-else class="fallback-content">
    {{ message.content }}
  </div>
</template>

<script setup>
import { ref, computed } from "vue"
import { useMessageParsing } from "./composables/useMessageParsing"
import { useChatStore } from "@/stores/baseStore"

// 接收props
const props = defineProps({
  message: {
    type: Object,
    required: true,
  },
})

// 响应式数据
const thinkCollapsed = ref(false) // 思考过程默认折叠

// 使用 store
const chatStore = useChatStore()

// 使用 composable
const messageRef = computed(() => props.message)
const { parsedAIMessage: parsedMessage } = useMessageParsing(messageRef)

// 计算是否显示加载状态
const isLoading = computed(() => {
  // 如果消息内容为空或者只有空白字符，并且当前正在流式响应或加载中，则显示加载状态
  const hasContent = props.message.content && props.message.content.trim()
  const isCurrentlyProcessing = chatStore.isLoading || chatStore.isStreamingActive

  // 如果内容是错误消息，不显示加载状态
  const isErrorMessage = hasContent && props.message.content.includes('❌')

  return !hasContent && isCurrentlyProcessing && !isErrorMessage
})

// 切换思考过程显示状态
const toggleThinkCollapse = () => {
  thinkCollapsed.value = !thinkCollapsed.value
}

// 过渡动画钩子函数
const onEnter = (el) => {
  el.style.height = "0"
  el.style.overflow = "hidden"

  // 强制重排
  el.offsetHeight

  // 设置目标高度
  el.style.height = el.scrollHeight + "px"
}

const onLeave = (el) => {
  el.style.height = el.scrollHeight + "px"
  el.style.overflow = "hidden"

  // 强制重排
  el.offsetHeight

  // 设置目标高度为0
  el.style.height = "0"
}
</script>

<style scoped>
/* 加载状态样式 */
.ai-loading-state {
  display: flex;
  align-items: center;
  width: 100%; /* 占满外层容器的宽度 */
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap; /* 确保内容在一行显示 */
  width: 100%; /* 确保内容占满容器宽度 */
  justify-content: flex-start; /* 内容左对齐 */
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #667eea;
  animation: loading-bounce 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

.dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes loading-bounce {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-text {
  font-size: 15px;
  color: #2d3748;
  font-weight: 400;
  white-space: nowrap; /* 确保文字不换行 */
  flex-shrink: 0; /* 防止文字被压缩 */
}

/* AI消息特殊内容样式 */
.ai-message-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 思考过程标题样式 */
.think-header {
  cursor: pointer;
  user-select: none;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.think-header:hover {
  opacity: 0.8;
}

.think-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  padding: 6px 0;
  font-family: "Inter", "SF Pro Text", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Helvetica Neue", Arial, sans-serif;
  letter-spacing: 0.2px;
}

.think-toggle-icon {
  transition: transform 0.2s ease;
  color: #9ca3af;
}

.think-label {
  color: #6b7280;
  font-family: "Inter", "SF Pro Text", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Helvetica Neue", Arial, sans-serif;
  font-weight: 500;
  letter-spacing: 0.2px;
}

.think-time {
  color: #9ca3af;
  font-weight: 400;
  font-family: "Microsoft YaHei", "微软雅黑", "Inter", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
  letter-spacing: 0.1px;
}

/* Think标签样式 - 类似DeepSeek的思考过程 */
.think-content {
  font-size: 12px;
  color: #6b7280;
  background: rgba(249, 250, 251, 0.8);
  padding: 14px 18px;
  border-radius: 8px;
  margin: 0px 0 0px 0;
  line-height: 1.65;
  font-weight: 400;
  letter-spacing: 0.3px;
  border: 1px solid rgba(229, 231, 235, 0.6);
  white-space: pre-wrap;
  word-wrap: break-word;
  transition: all 0.3s ease;
  /* 深度思考专用字体样式 */
  font-family: "Microsoft YaHei", "微软雅黑", "Inter", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 增强可读性 */
  word-spacing: 0.1em;
  text-align: justify;
  text-justify: inter-word;
}

/* 思考内容包装器 */
.think-wrapper {
  overflow: hidden;
}

/* 思考折叠过渡动画 */
.think-collapse-enter-active,
.think-collapse-leave-active {
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.think-collapse-enter-from,
.think-collapse-leave-to {
  height: 0;
  overflow: hidden;
}

.think-collapse-enter-to,
.think-collapse-leave-from {
  overflow: hidden;
}

/* 流式思考内容样式 */
.think-content-streaming {
  border-left: 3px solid #3b82f6;
  background: rgba(239, 246, 255, 0.8);
}

/* 流式指示器 */
.streaming-indicator {
  color: #3b82f6;
  font-weight: bold;
  animation: blink 1s infinite;
  margin-left: 2px;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* Answer标签样式 - 正常的回答内容 */
.answer-content {
  font-size: 17px;
  color: #1f2937;
  line-height: 1.6;
  font-weight: 400;
  margin: 4px 0;
  word-wrap: break-word;
}

/* 普通文本内容 */
.normal-content {
  font-size: 17px;
  color: #1f2937;
  line-height: 1.6;
  font-weight: 400;
  margin: 4px 0;
  word-wrap: break-word;
}

/* 备用内容样式 */
.fallback-content {
  font-size: 17px;
  color: #1f2937;
  line-height: 1.6;
  font-weight: 400;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Markdown 渲染样式 */
.markdown-rendered {
  font-size: 17px;
  color: #1f2937;
  line-height: 1.6;
  font-weight: 400;
}

/* Markdown 元素样式 */
.markdown-rendered :deep(h1),
.markdown-rendered :deep(h2),
.markdown-rendered :deep(h3),
.markdown-rendered :deep(h4),
.markdown-rendered :deep(h5),
.markdown-rendered :deep(h6) {
  margin: 6px 0 2px 0;
  font-weight: 600;
  line-height: 1.2;
  color: #1f2937;
}

.markdown-rendered :deep(h1) {
  font-size: 24px;
}
.markdown-rendered :deep(h2) {
  font-size: 20px;
}
.markdown-rendered :deep(h3) {
  font-size: 18px;
}
.markdown-rendered :deep(h4) {
  font-size: 16px;
}
.markdown-rendered :deep(h5) {
  font-size: 14px;
}
.markdown-rendered :deep(h6) {
  font-size: 13px;
}

.markdown-rendered :deep(p) {
  margin: 2px 0;
  line-height: 1.4;
}

.markdown-rendered :deep(ul),
.markdown-rendered :deep(ol) {
  margin: 4px 0;
  padding-left: 20px;
  list-style-position: outside;
}

.markdown-rendered :deep(ol) {
  counter-reset: list-counter;
}

.markdown-rendered :deep(li) {
  margin: 3px 0;
  line-height: 1.4;
  padding-left: 4px;
}

.markdown-rendered :deep(ol > li) {
  display: list-item;
  list-style-type: decimal;
}

.markdown-rendered :deep(ul > li) {
  display: list-item;
  list-style-type: disc;
}

.markdown-rendered :deep(blockquote) {
  margin: 6px 0;
  padding: 6px 12px;
  border-left: 3px solid #e5e7eb;
  background-color: #f9fafb;
  color: #6b7280;
  font-style: italic;
}

.markdown-rendered :deep(code) {
  background-color: #f3f4f6;
  color: #e11d48;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: "Consolas", "Monaco", "Courier New", monospace;
  font-size: 13px;
}

.markdown-rendered :deep(pre) {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 6px 0;
  font-family: "Consolas", "Monaco", "Courier New", monospace;
  font-size: 13px;
  line-height: 1.3;
}

.markdown-rendered :deep(pre code) {
  background: none;
  color: inherit;
  padding: 0;
  border-radius: 0;
}

.markdown-rendered :deep(table) {
  border-collapse: collapse;
  margin: 6px 0;
  width: 100%;
}

.markdown-rendered :deep(th),
.markdown-rendered :deep(td) {
  border: 1px solid #e5e7eb;
  padding: 6px 10px;
  text-align: left;
}

.markdown-rendered :deep(th) {
  background-color: #f9fafb;
  font-weight: 600;
}

.markdown-rendered :deep(a) {
  color: #3b82f6;
  text-decoration: none;
}

.markdown-rendered :deep(a:hover) {
  text-decoration: underline;
}

.markdown-rendered :deep(strong) {
  font-weight: 600;
  color: #1f2937;
}

.markdown-rendered :deep(em) {
  font-style: italic;
}

/* 列表项中的粗体标题样式 */
.markdown-rendered :deep(li strong) {
  font-weight: 700;
  color: #111827;
}

/* 确保列表项内容对齐 */
.markdown-rendered :deep(li p) {
  margin: 0;
  display: inline;
}

.markdown-rendered :deep(hr) {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 8px 0;
}

/* 自定义容器样式 */
.markdown-rendered :deep(.custom-block) {
  margin: 6px 0;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid;
}

.markdown-rendered :deep(.custom-block-title) {
  font-weight: 600;
  margin: 0 0 4px 0;
}

.markdown-rendered :deep(.warning) {
  background-color: #fef3cd;
  border-left-color: #f59e0b;
  color: #92400e;
}

.markdown-rendered :deep(.tip) {
  background-color: #dbeafe;
  border-left-color: #3b82f6;
  color: #1e40af;
}

/* 文本内容样式 */
.text-content {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* ===== 深色主题适配 ===== */
[data-theme="dark"] .think-content {
  background: rgba(45, 45, 45, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--app-text-secondary);
  /* 深色主题下的深度思考字体优化 */
  font-family: "Microsoft YaHei", "微软雅黑", "Inter", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
  letter-spacing: 0.3px;
  line-height: 1.65;
  word-spacing: 0.1em;
  text-align: justify;
  text-justify: inter-word;
}

[data-theme="dark"] .think-content-streaming {
  background: rgba(59, 130, 246, 0.1);
  border-left: 3px solid #3b82f6;
}

[data-theme="dark"] .answer-content,
[data-theme="dark"] .normal-content,
[data-theme="dark"] .fallback-content,
[data-theme="dark"] .markdown-rendered {
  color: var(--app-text-primary);
}

[data-theme="dark"] .markdown-rendered :deep(h1),
[data-theme="dark"] .markdown-rendered :deep(h2),
[data-theme="dark"] .markdown-rendered :deep(h3),
[data-theme="dark"] .markdown-rendered :deep(h4),
[data-theme="dark"] .markdown-rendered :deep(h5),
[data-theme="dark"] .markdown-rendered :deep(h6) {
  color: var(--app-text-primary);
}

[data-theme="dark"] .markdown-rendered :deep(blockquote) {
  background-color: rgba(45, 45, 45, 0.5);
  border-left: 3px solid rgba(255, 255, 255, 0.3);
  color: var(--app-text-secondary);
}

[data-theme="dark"] .markdown-rendered :deep(code) {
  background-color: rgba(45, 45, 45, 0.8);
  color: #ff6b6b;
}

[data-theme="dark"] .markdown-rendered :deep(th),
[data-theme="dark"] .markdown-rendered :deep(td) {
  border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .markdown-rendered :deep(th) {
  background-color: rgba(45, 45, 45, 0.8);
}

[data-theme="dark"] .markdown-rendered :deep(strong) {
  color: var(--app-text-primary);
}

[data-theme="dark"] .markdown-rendered :deep(li strong) {
  color: var(--app-text-primary);
}

[data-theme="dark"] .markdown-rendered :deep(hr) {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .markdown-rendered :deep(.warning) {
  background-color: rgba(245, 158, 11, 0.1);
  border-left-color: #f59e0b;
  color: #fbbf24;
}

[data-theme="dark"] .markdown-rendered :deep(.tip) {
  background-color: rgba(59, 130, 246, 0.1);
  border-left-color: #3b82f6;
  color: #60a5fa;
}
</style>
