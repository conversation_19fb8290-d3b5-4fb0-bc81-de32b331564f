// Plugins
import vue from "@vitejs/plugin-vue"
import vuetify, { transformAssetUrls } from "vite-plugin-vuetify"
// import viteImagemin from "vite-plugin-imagemin"

// Utilities
import { defineConfig } from "vite"
import { fileURLToPath, URL } from "node:url"

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue({
      template: { transformAssetUrls },
    }),
    // https://github.com/vuetifyjs/vuetify-loader/tree/next/packages/vite-plugin
    vuetify({
      autoImport: true,
    }),
    // 图片压缩优化 - 暂时注释掉，避免依赖问题
    // viteImagemin({
    //   gifsicle: {
    //     optimizationLevel: 7,
    //     interlaced: false,
    //   },
    //   optipng: {
    //     optimizationLevel: 7,
    //   },
    //   mozjpeg: {
    //     quality: 80,
    //   },
    //   pngquant: {
    //     quality: [0.65, 0.8],
    //     speed: 4,
    //   },
    //   webp: {
    //     quality: 80,
    //   },
    // }),
  ],
  // 构建配置
  build: {
    // 生产环境移除 console
    minify: "terser",
    terserOptions: {
      compress: {
        // 生产环境时移除console
        drop_console: true,
        drop_debugger: true,
      },
    },
  },
  define: { "process.env": {} },
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
    extensions: [".js", ".json", ".jsx", ".mjs", ".ts", ".tsx", ".vue"],
  },
  server: {
    port: 3000,
    host: "0.0.0.0", // 允许外部访问
    proxy: {
      "/api": {
        // target: "http://*************:8080",
        target: "http://********:14580",
        changeOrigin: true, // 修改请求头中的 origin 为 target
        secure: false, // 如果是https接口，需要配置这个参数
        rewrite: (path) => path, // 可选：路径重写
      },
      "/ws": {
        target: "ws://*************:8080", // WebSocket代理
        ws: true, // 启用WebSocket代理
        changeOrigin: true,
      },
    },
  },
})
