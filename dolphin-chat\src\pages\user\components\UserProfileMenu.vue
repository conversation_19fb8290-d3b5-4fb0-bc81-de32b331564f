<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue"
import { useRouter } from "vue-router"
import { useAuthStore } from "@/stores/authstore"
import { UserService } from "@/api/user"
import informationIconUrl from "@/assets/icons/Information.svg"

// Props
const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false,
  },
})

// Emits
const emit = defineEmits(["settings-open"])

// 状态管理
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const userMenuOpen = ref(false)
const avatarUploadLoading = ref(false)
const fileInputRef = ref(null)

// 响应式数据 - 用户头像URL
const userAvatarUrl = ref(null)

// 计算属性 - 获取用户头像
const userAvatar = computed(() => {
  const avatar = userAvatarUrl.value || authStore.user?.avatar || null

  return avatar
})

// 计算属性 - 获取用户名
const userName = computed(() => {
  return authStore.user?.username || authStore.user?.name || "用户"
})

// 方法
const openProfile = () => {
  userMenuOpen.value = !userMenuOpen.value
}

const openSettings = () => {
  userMenuOpen.value = false
  emit("settings-open")
}

const openContact = () => {
  userMenuOpen.value = false
  console.log("联系我们")
}

const handleLogout = () => {
  userMenuOpen.value = false
  authStore.logout()
  router.push({ name: "login" })
}

// 头像相关方法
const loadUserAvatar = async () => {
  try {
    // 清理之前的blob URL
    if (userAvatarUrl.value && userAvatarUrl.value.startsWith("blob:")) {
      UserService.revokeAvatarUrl(userAvatarUrl.value)
    }

    const result = await UserService.getUserAvatar()

    if (result.success && result.data.avatarUrl) {
      userAvatarUrl.value = result.data.avatarUrl
    } else {
      console.error("获取头像URL失败:", result.message)
    }
  } catch (error) {
    console.error("获取头像URL失败:", error)
  }
}

const triggerAvatarUpload = () => {
  // 如果侧边栏处于收缩状态，不允许上传头像
  if (props.isCollapsed) {
    return
  }
  fileInputRef.value?.click()
}

const handleAvatarUpload = async (event) => {
  const file = event.target.files?.[0]
  if (!file) return

  try {
    avatarUploadLoading.value = true

    // 调用头像上传API
    const result = await UserService.uploadAvatar(file)

    if (result.success) {
      // 重新加载头像URL
      await loadUserAvatar()
    } else {
      console.error("头像上传失败:", result.message)
      // 可以添加错误提示
    }
  } catch (error) {
    console.error("头像上传错误:", error)
    // 可以添加错误提示
  } finally {
    avatarUploadLoading.value = false
    // 清空文件输入，允许重复上传同一文件
    if (fileInputRef.value) {
      fileInputRef.value.value = ""
    }
  }
}

// 组件挂载时获取用户信息
onMounted(async () => {
  // 如果用户已登录，加载头像
  if (authStore.isLoggedIn) {
    await loadUserAvatar()
  } else {
    console.log("用户未登录，跳过头像加载")
  }
})

// 组件卸载时清理blob URL
onUnmounted(() => {
  if (userAvatarUrl.value && userAvatarUrl.value.startsWith("blob:")) {
    UserService.revokeAvatarUrl(userAvatarUrl.value)
  }
})
</script>

<template>
  <!-- 隐藏的文件输入 -->
  <input
    ref="fileInputRef"
    type="file"
    accept="image/*"
    style="display: none"
    @change="handleAvatarUpload" />

  <!-- 展开状态的个人资料菜单 -->
  <VMenu
    v-if="!isCollapsed"
    v-model="userMenuOpen"
    :close-on-content-click="false"
    location="top end"
    offset="8">
    <template #activator="{ props: activatorProps }">
      <VListItem
        v-bind="activatorProps"
        title="个人资料"
        :append-icon="userMenuOpen ? 'mdi-chevron-up' : 'mdi-chevron-down'">
        <template #prepend>
          <VAvatar
            size="32"
            class="profile-icon-avatar clickable-avatar"
            @click.stop="triggerAvatarUpload">
            <!-- 显示用户头像或默认图标 -->
            <VImg
              v-if="userAvatar"
              :src="userAvatar"
              :alt="userName"
              class="user-avatar-img"
              cover />
            <img v-else :src="informationIconUrl" alt="个人资料" class="profile-icon" />

            <!-- 上传加载状态 -->
            <VProgressCircular
              v-if="avatarUploadLoading"
              indeterminate
              size="24"
              width="2"
              color="primary"
              class="upload-loading" />
          </VAvatar>
        </template>
      </VListItem>
    </template>

    <VCard min-width="200" class="user-menu-card">
      <!-- 菜单项 -->
      <VList density="compact" class="user-menu-list">
        <VListItem
          prepend-icon="mdi-cog-outline"
          title="系统设置"
          class="menu-item"
          @click="openSettings" />
        <VListItem
          prepend-icon="mdi-email-outline"
          title="联系我们"
          class="menu-item"
          @click="openContact" />
        <VListItem
          prepend-icon="mdi-logout"
          title="退出登录"
          class="menu-item"
          @click="handleLogout" />
      </VList>
    </VCard>
  </VMenu>

  <!-- 收缩状态的个人资料菜单 -->
  <VMenu
    v-if="isCollapsed"
    v-model="userMenuOpen"
    :close-on-content-click="false"
    location="top end"
    offset="8">
    <template #activator="{ props: activatorProps }">
      <VBtn
        v-bind="activatorProps"
        variant="text"
        size="default"
        class="collapsed-menu-btn profile-icon-btn"
        @click.stop="triggerAvatarUpload">
        <!-- 显示用户头像或默认图标 -->
        <VAvatar size="24" class="collapsed-avatar">
          <VImg v-if="userAvatar" :src="userAvatar" :alt="userName" class="user-avatar-img" cover />
          <img v-else :src="informationIconUrl" alt="个人资料" class="profile-icon-small" />

          <!-- 上传加载状态 -->
          <VProgressCircular
            v-if="avatarUploadLoading"
            indeterminate
            size="20"
            width="2"
            color="primary"
            class="upload-loading" />
        </VAvatar>
      </VBtn>
    </template>

    <VCard min-width="200" class="user-menu-card">
      <!-- 菜单项 -->
      <VList density="compact" class="user-menu-list">
        <VListItem
          prepend-icon="mdi-cog-outline"
          title="系统设置"
          class="menu-item"
          @click="openSettings" />
        <VListItem
          prepend-icon="mdi-email-outline"
          title="联系我们"
          class="menu-item"
          @click="openContact" />
        <VListItem
          prepend-icon="mdi-logout"
          title="退出登录"
          class="menu-item"
          @click="handleLogout" />
      </VList>
    </VCard>
  </VMenu>
</template>

<style scoped>
/* 用户菜单样式 */
.user-menu-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
}

.user-menu-list {
  padding: 8px 0;
}

.menu-item {
  padding: 8px 16px;
  min-height: 40px;
}

.menu-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.menu-item .v-list-item__prepend {
  margin-right: 12px;
}

.menu-item .v-icon {
  color: #666;
}

.collapsed-menu-btn {
  width: 100%;
  color: rgb(var(--v-theme-on-surface)) !important;
  opacity: 0.8;
}

.collapsed-menu-btn:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.05);
}

/* 个人资料图标样式 */
.profile-icon-avatar {
  background-color: transparent !important;
  position: relative;
}

.profile-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  /* filter样式现在由主题CSS统一管理 */
}

.profile-icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  min-width: 44px;
  border-radius: 8px;
  padding: 0;
  margin: 0 auto;
}

.profile-icon-small {
  width: 22px;
  height: 22px;
  object-fit: contain;
  /* filter样式现在由主题CSS统一管理 */
}

/* 头像相关样式 */
.clickable-avatar {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.clickable-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.user-avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.collapsed-avatar {
  background-color: transparent !important;
  position: relative;
}

.upload-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
}
</style>
