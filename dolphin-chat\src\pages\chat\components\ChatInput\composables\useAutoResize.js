import { ref, nextTick } from 'vue'

/**
 * 自动调整大小相关逻辑
 */
export function useAutoResize(externalTextareaRef) {
  const textareaRef = externalTextareaRef || ref(null)

  /**
   * 自动调整textarea高度
   */
  const autoResize = () => {
    nextTick(() => {
      // 获取实际的 textarea 元素
      const textarea = textareaRef.value?.textareaRef || textareaRef.value
      if (textarea) {
        // 重置高度以获取正确的scrollHeight
        textarea.style.height = 'auto'
        // 设置新高度，最小40px，最大120px
        const newHeight = Math.min(Math.max(textarea.scrollHeight, 40), 120)
        textarea.style.height = newHeight + 'px'
      }
    })
  }

  /**
   * 重置textarea高度到初始状态
   */
  const resetTextareaHeight = () => {
    nextTick(() => {
      // 获取实际的 textarea 元素
      const textarea = textareaRef.value?.textareaRef || textareaRef.value
      if (textarea) {
        // 重置到初始高度
        textarea.style.height = '40px'
      }
    })
  }

  /**
   * 滚动到底部的方法 - 支持对比模式
   */
  const scrollToBottomImmediate = () => {
    nextTick(() => {
      // 检查是否为对比模式
      const isComparisonMode = document.querySelector('.comparison-layout')

      if (isComparisonMode) {
        // 对比模式：滚动左右两侧的消息容器
        const leftContainer = document.querySelector('.comparison-left .messages-container')
        const rightContainer = document.querySelector('.comparison-right .messages-container')

        // 滚动左侧容器
        if (leftContainer) {
          const scrollHeight = leftContainer.scrollHeight
          const clientHeight = leftContainer.clientHeight
          const maxScrollTop = scrollHeight - clientHeight
          leftContainer.scrollTo({
            top: maxScrollTop + 100, // 增加更多偏移量确保完全可见
            behavior: 'smooth',
          })
        }

        // 滚动右侧容器
        if (rightContainer) {
          const scrollHeight = rightContainer.scrollHeight
          const clientHeight = rightContainer.clientHeight
          const maxScrollTop = scrollHeight - clientHeight
          rightContainer.scrollTo({
            top: maxScrollTop + 100, // 增加更多偏移量确保完全可见
            behavior: 'smooth',
          })
        }
      } else {
        // 普通模式：滚动主聊天容器
        const chatMessagesContainer = document.querySelector('.chat-messages')
        if (chatMessagesContainer) {
          const scrollHeight = chatMessagesContainer.scrollHeight
          const clientHeight = chatMessagesContainer.clientHeight
          const maxScrollTop = scrollHeight - clientHeight

          chatMessagesContainer.scrollTo({
            top: maxScrollTop + 100, // 额外增加100px确保完全可见
            behavior: 'smooth',
          })
        }
      }
    })
  }

  return {
    // 响应式数据
    textareaRef,

    // 方法
    autoResize,
    resetTextareaHeight,
    scrollToBottomImmediate,
  }
}
