<script setup>
import { ref, computed, watch } from "vue"
import { useChatStore } from "@/stores/baseStore"

// 定义 props
const props = defineProps({
  side: {
    type: String,
    required: true,
    validator: (value) => ['left', 'right'].includes(value)
  },
  compact: {
    type: Boolean,
    default: false,
  },
  showFullName: {
    type: Boolean,
    default: true,
  },
})

// 使用聊天store
const chatStore = useChatStore()

// 响应式数据
const showDropdown = ref(false)

// 计算属性
const selectedModel = computed(() => {
  if (props.side === 'left') {
    return chatStore.leftModelInfo
  } else {
    return chatStore.rightModelInfo
  }
})

const availableModels = computed(() => chatStore.availableModels)
const modelsLoading = computed(() => chatStore.modelsLoading)
const modelsError = computed(() => chatStore.modelsError)

// 显示文本的计算属性
const displayText = computed(() => {
  const model = selectedModel.value
  if (!model) return "选择模型"

  if (props.compact) {
    // 紧凑模式：只显示简短名称
    return model.shortName || model.name.split(" ")[0] || model.name
  } else if (!props.showFullName) {
    // 中等模式：显示主要名称
    return model.name
  } else {
    // 完整模式：显示完整名称
    return model.name
  }
})

// 方法
const selectModel = (modelId) => {
  if (props.side === 'left') {
    chatStore.setLeftModel(modelId)
  } else {
    chatStore.setRightModel(modelId)
  }
  showDropdown.value = false
}

// 重新获取模型列表
const refreshModels = () => {
  chatStore.fetchModels()
}

// 清除错误
const clearError = () => {
  chatStore.clearModelsError()
}

// 监听模型变化，确保响应式更新
watch(
  () => selectedModel.value,
  (newModel, oldModel) => {
    if (newModel?.id !== oldModel?.id) {
    }
  },
  { immediate: true }
)

// 生命周期钩子 - VMenu 会自动处理点击外部关闭，所以不需要手动处理
</script>

<template>
  <div class="comparison-model-selector">
    <!-- 使用 Vuetify 的 VMenu 组件 -->
    <VMenu v-model="showDropdown" :close-on-content-click="false" location="bottom">
      <template v-slot:activator="{ props }">
        <div class="model-selector-btn" v-bind="props">
          <span class="model-name">{{ displayText }}</span>
          <VIcon
            :icon="showDropdown ? 'mdi-chevron-up' : 'mdi-chevron-down'"
            size="16"
            class="dropdown-icon" />
        </div>
      </template>

      <!-- 下拉菜单内容 -->
      <VCard class="model-dropdown-card">
        <div class="dropdown-header">
          <span class="dropdown-title">选择{{ side === 'left' ? '左侧' : '右侧' }}模型</span>
          <VBtn
            v-if="modelsError"
            icon="mdi-refresh"
            size="small"
            variant="text"
            color="primary"
            @click="refreshModels"
            :loading="modelsLoading"
            class="refresh-btn" />
        </div>
        <!-- 加载状态 -->
        <div v-if="modelsLoading" class="loading-container">
          <VProgressCircular indeterminate color="primary" size="24" />
          <span class="loading-text">加载模型列表中...</span>
        </div>
        <!-- 错误状态 -->
        <div v-else-if="modelsError" class="error-container">
          <VIcon icon="mdi-alert-circle" color="error" size="24" />
          <span class="error-text">{{ modelsError }}</span>
          <VBtn size="small" color="primary" @click="refreshModels" :loading="modelsLoading">
            重试
          </VBtn>
        </div>

        <!-- 模型列表 -->
        <div v-else class="model-list">
          <div
            v-for="model in availableModels"
            :key="model.id"
            class="model-item"
            :class="{ selected: model.id === selectedModel?.id }"
            @click="selectModel(model.id)">
            <VIcon :icon="model.icon" :color="model.color" size="20" class="model-item-icon" />
            <div class="model-item-content">
              <div class="model-item-name">{{ model.name }}</div>
              <div class="model-item-desc">{{ model.description }}</div>
            </div>
            <div class="model-item-actions">
              <VIcon
                v-if="model.id === selectedModel?.id"
                icon="mdi-check"
                color="primary"
                size="16"
                class="check-icon" />
            </div>
          </div>
        </div>
      </VCard>
    </VMenu>
  </div>
</template>

<style scoped>
.comparison-model-selector {
  position: relative;
  display: inline-block;
  width: 100%;
  min-height: 40px;
}

.model-selector-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--app-bg-primary);
  border: 1px solid var(--app-border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
  width: 100%;
  justify-content: space-between;
  box-shadow: 0 1px 3px var(--app-shadow-light);
}

.model-selector-btn:hover {
  background: var(--app-hover-bg);
  border-color: var(--app-border-color);
}

.model-selector-btn.active {
  border-color: rgb(var(--v-theme-primary));
  box-shadow: 0 0 0 2px rgba(var(--v-theme-primary), 0.1);
}

.model-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--app-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-icon {
  color: var(--app-text-secondary);
  transition: transform 0.2s ease;
}

.model-selector-btn.active .dropdown-icon {
  transform: rotate(180deg);
}

.model-dropdown-card {
  min-width: 280px;
  max-width: 320px;
  border-radius: 12px;
  overflow: hidden;
}

.dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--app-border-color);
  background: var(--app-bg-secondary);
}

.dropdown-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--app-text-primary);
}

.loading-container,
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 24px 16px;
  color: var(--app-text-secondary);
}

.loading-text,
.error-text {
  font-size: 14px;
}

.model-list {
  max-height: 300px;
  overflow-y: auto;
}

.model-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.model-item:hover {
  background: var(--app-bg-hover);
}

.model-item.selected {
  background: rgba(var(--v-theme-primary), 0.08);
}

.model-item-icon {
  flex-shrink: 0;
}

.model-item-content {
  flex: 1;
  min-width: 0;
}

.model-item-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--app-text-primary);
  margin-bottom: 2px;
}

.model-item-desc {
  font-size: 12px;
  color: var(--app-text-secondary);
  line-height: 1.4;
}

.model-item-actions {
  flex-shrink: 0;
}

.check-icon {
  opacity: 0.8;
}

/* VMenu 自带动画，不需要自定义过渡动画 */
</style>
