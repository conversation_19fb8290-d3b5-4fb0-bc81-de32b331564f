<script setup>
import { computed, ref, watch, onMounted, onUnmounted } from "vue"
import { useChatStore } from "@/stores/baseStore"

const chatStore = useChatStore()

// 计算属性
const showImageCanvas = computed(() => chatStore.showImageCanvas)
const canvasImages = computed(() => chatStore.canvasImages)

// 响应式数据
const currentImageIndex = ref(0)
const selectedImage = ref(null)
const showImageDialog = ref(false)

// 计算当前显示的图片
const currentImage = computed(() => {
  if (canvasImages.value.length === 0) return null
  return canvasImages.value[currentImageIndex.value] || null
})

// 监听图片列表变化，重置索引
watch(
  canvasImages,
  (newImages) => {
    if (newImages.length === 0) {
      currentImageIndex.value = 0
    } else if (currentImageIndex.value >= newImages.length) {
      currentImageIndex.value = newImages.length - 1
    }
  },
  { immediate: true }
)

// 方法
const closeCanvas = () => {
  chatStore.hideImageCanvas()
}

const openImagePreview = (image) => {
  selectedImage.value = image
  showImageDialog.value = true
}

const closeImagePreview = () => {
  showImageDialog.value = false
  selectedImage.value = null
}

const downloadImage = (image) => {
  const link = document.createElement("a")
  link.href = image.url
  link.download = image.name || "image.png"
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const handleImageError = (event) => {
  event.target.src = "/placeholder-image.png"
  console.warn("图片加载失败:", event.target.src)
}

// 切换图片方法
const previousImage = () => {
  if (canvasImages.value.length <= 1) return
  currentImageIndex.value =
    currentImageIndex.value > 0 ? currentImageIndex.value - 1 : canvasImages.value.length - 1
}

const nextImage = () => {
  if (canvasImages.value.length <= 1) return
  currentImageIndex.value =
    currentImageIndex.value < canvasImages.value.length - 1 ? currentImageIndex.value + 1 : 0
}

// 键盘导航
const handleKeydown = (event) => {
  if (!showImageCanvas.value) return

  switch (event.key) {
    case "ArrowLeft":
      event.preventDefault()
      previousImage()
      break
    case "ArrowRight":
      event.preventDefault()
      nextImage()
      break
    case "Escape":
      event.preventDefault()
      closeCanvas()
      break
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener("keydown", handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown)
})
</script>

<template>
  <div class="image-canvas" :class="{ show: showImageCanvas }">
    <!-- 画布头部 -->
    <div class="canvas-header">
      <div class="header-left">
        <div class="header-title">
          <VIcon icon="mdi-image-outline" size="20" />
          <span>图片画布</span>
          <VChip size="x-small" color="primary">{{ canvasImages.length }}</VChip>
        </div>
        <!-- 图片信息在头部显示 -->
        <div class="header-image-info" v-if="currentImage">
          <span class="image-name-header">{{ currentImage.name || "未知图片" }}</span>
        </div>
      </div>

      <!-- 图片导航控制在头部 -->
      <div class="header-navigation" v-if="canvasImages.length > 1">
        <VBtn
          icon="mdi-chevron-left"
          size="small"
          variant="text"
          @click="previousImage"
          :disabled="canvasImages.length <= 1"
          title="上一张 (←)" />
        <div class="image-counter-header">
          {{ currentImageIndex + 1 }} / {{ canvasImages.length }}
        </div>
        <VBtn
          icon="mdi-chevron-right"
          size="small"
          variant="text"
          @click="nextImage"
          :disabled="canvasImages.length <= 1"
          title="下一张 (→)" />
      </div>

      <VBtn
        icon="mdi-close"
        size="small"
        variant="text"
        class="image-canvas-close-btn"
        @click="closeCanvas" />
    </div>

    <!-- 图片展示区域 -->
    <div class="canvas-content" v-if="canvasImages.length > 0 && currentImage">
      <!-- 当前图片显示 -->
      <div class="current-image-container">
        <div class="image-wrapper">
          <img
            :src="currentImage.url"
            :alt="currentImage.name"
            class="canvas-image"
            @error="handleImageError"
            @click="openImagePreview(currentImage)" />
          <div class="image-overlay">
            <div class="image-actions">
              <VBtn
                icon="mdi-eye-outline"
                size="small"
                variant="text"
                color="white"
                @click.stop="openImagePreview(currentImage)"
                title="预览" />
              <VBtn
                icon="mdi-download-outline"
                size="small"
                variant="text"
                color="white"
                @click.stop="downloadImage(currentImage)"
                title="下载" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <VIcon icon="mdi-image-off-outline" size="48" color="grey-lighten-1" />
      <p class="empty-text">暂无图片</p>
      <p class="empty-hint">点击AI消息下的图片按钮来显示询问的图片</p>
    </div>

    <!-- 图片预览对话框 -->
    <VDialog v-model="showImageDialog" max-width="800" @click:outside="closeImagePreview">
      <VCard v-if="selectedImage">
        <VCardTitle class="d-flex justify-space-between align-center">
          <span>{{ selectedImage.name || "图片预览" }}</span>
          <VBtn icon="mdi-close" size="small" variant="text" @click="closeImagePreview" />
        </VCardTitle>
        <VCardText class="pa-0">
          <img :src="selectedImage.url" :alt="selectedImage.name" class="preview-image" />
        </VCardText>
        <VCardActions>
          <VBtn
            prepend-icon="mdi-download-outline"
            variant="outlined"
            @click="downloadImage(selectedImage); closeImagePreview()"
            >
            下载
          </VBtn>
          <VSpacer />
          <VBtn variant="text" @click="closeImagePreview"> 关闭 </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>

<style scoped>
.image-canvas {
  position: fixed;
  top: 0;
  right: -650px;
  width: 650px;
  height: 100vh;
  background: var(--app-bg-primary);
  border-left: 1px solid var(--app-border-color);
  box-shadow: -2px 0 12px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease, background-color 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.image-canvas.show {
  right: 0;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--app-border-color);
  background: var(--app-bg-secondary);
  min-height: 60px;
  gap: 12px;
}

/* 图片画布关闭按钮样式 */
.image-canvas-close-btn {
  background: var(--app-bg-primary) !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  z-index: 1002 !important;
}

.image-canvas-close-btn:hover {
  background: var(--app-hover-bg) !important;
  transform: scale(1.05);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--app-text-primary);
}

.header-image-info {
  display: flex;
  align-items: center;
}

.image-name-header {
  font-size: 12px;
  color: var(--app-text-secondary);
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.header-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--app-bg-primary);
  border-radius: 6px;
  padding: 4px 8px;
  border: 1px solid var(--app-border-color);
}

.image-counter-header {
  font-size: 12px;
  font-weight: 500;
  color: var(--app-text-primary);
  min-width: 40px;
  text-align: center;
}

.canvas-content {
  flex: 1;
  overflow: hidden; /* 防止滚动，让图片占满空间 */
  padding: 6px; /* 移除内边距，让图片占据更多空间 */
  display: flex;
  flex-direction: column;
}

.current-image-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.image-wrapper {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--app-bg-secondary);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  min-height: calc(100vh - 180px); /* 减去头部、导航、信息区域的高度 */
  max-height: calc(100vh - 180px);
}

.image-wrapper:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.canvas-image {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 保持宽高比，完全显示图片 */
  cursor: pointer;
  transition: transform 0.2s ease;
  border-radius: 8px;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 12px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-wrapper:hover .image-overlay {
  opacity: 1;
}

.image-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  word-break: break-all;
}

.image-actions {
  display: flex;
  gap: 8px;
}

.image-info {
  padding: 8px 12px;
  background: var(--app-bg-secondary);
  border-radius: 6px;
  border: 1px solid var(--app-border-color);
  flex-shrink: 0; /* 防止信息区域被压缩 */
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 2px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 11px;
  font-weight: 500;
  color: var(--app-text-secondary);
  min-width: 35px;
}

.info-value {
  font-size: 11px;
  color: var(--app-text-primary);
  word-break: break-all;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 32px;
}

.empty-text {
  margin: 16px 0 8px 0;
  font-size: 18px;
  font-weight: 500;
  color: var(--app-text-secondary);
}

.empty-hint {
  margin: 0;
  font-size: 14px;
  color: var(--app-text-tertiary);
  line-height: 1.5;
}

.preview-image {
  width: 100%;
  height: auto;
  max-height: 70vh;
  object-fit: contain;
  display: block;
}

/* 滚动条样式 */
.canvas-content::-webkit-scrollbar {
  width: 8px;
}

.canvas-content::-webkit-scrollbar-track {
  background: transparent;
}

.canvas-content::-webkit-scrollbar-thumb {
  background: var(--app-border-color);
  border-radius: 4px;
}

.canvas-content::-webkit-scrollbar-thumb:hover {
  background: var(--app-text-tertiary);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .image-canvas {
    width: 450px;
    right: -450px;
  }

  .image-wrapper {
    min-height: calc(100vh - 160px);
    max-height: calc(100vh - 160px);
  }
}

@media (max-width: 768px) {
  .image-canvas {
    width: 100vw;
    right: -100vw;
  }

  .canvas-content {
    padding: 6px;
    gap: 6px;
  }

  .image-wrapper {
    min-height: calc(100vh - 140px);
    max-height: calc(100vh - 140px);
    border-radius: 6px;
  }

  .image-navigation {
    padding: 4px 8px;
  }

  .image-counter {
    font-size: 12px;
    min-width: 50px;
  }

  .image-info {
    padding: 6px 8px;
  }

  .info-label,
  .info-value {
    font-size: 10px;
  }
}
</style>
