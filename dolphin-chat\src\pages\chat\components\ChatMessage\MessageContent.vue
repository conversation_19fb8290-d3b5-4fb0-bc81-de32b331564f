<template>
  <div class="message-content" :class="{ 'editing-mode': isEditing && messageType === 'user' }">
    <!-- 单张图片消息内容 -->
    <ImageMessageContent
      v-if="messageType === 'image'"
      :image-url="message.imageUrl"
      :image-name="message.imageName"
      :caption="message.content"
      @open-preview="handleOpenImagePreview" />

    <!-- 多张图片消息内容 -->
    <MultiImageMessageContent
      v-else-if="messageType === 'multi-image'"
      :images="message.images"
      :caption="message.content"
      @open-preview="handleOpenMultiImagePreview" />

    <!-- 文本消息内容 -->
    <div v-else class="message-text">
      <!-- AI消息的特殊渲染 -->
      <AIMessageContent v-if="messageType === 'ai'" :message="message" />

      <!-- 普通消息渲染 -->
      <TextMessageContent
        v-else
        :content="message.content"
        :message="message"
        :is-editing="isEditing && messageType === 'user'"
        @finish-editing="handleFinishEditing"
        @cancel-editing="handleCancelEditing" />
    </div>
  </div>
</template>

<script setup>
// 导入子组件
import AIMessageContent from "./AIMessageContent.vue"
import ImageMessageContent from "./ImageMessageContent.vue"
import MultiImageMessageContent from "./MultiImageMessageContent.vue"
import TextMessageContent from "./TextMessageContent.vue"

// 接收props
const props = defineProps({
  message: {
    type: Object,
    required: true,
  },
  messageType: {
    type: String,
    required: true,
  },
  isEditing: {
    type: Boolean,
    default: false,
  },
})

// 定义emits
const emit = defineEmits(["open-image-preview", "open-multi-image-preview", "finish-editing", "cancel-editing"])

// 事件处理
const handleOpenImagePreview = () => {
  emit("open-image-preview")
}

const handleOpenMultiImagePreview = (data) => {
  emit("open-multi-image-preview", data)
}

const handleFinishEditing = (content) => {
  emit("finish-editing", content)
}

const handleCancelEditing = () => {
  emit("cancel-editing")
}
</script>

<style scoped>
/* 消息内容 */
.message-content {
  max-width: 100%;
  min-width: 120px;
  position: relative;
}

/* 编辑模式下的消息内容容器 */
.message-content.editing-mode {
  max-width: 600px; /* 编辑模式下增加最大宽度 */
  min-width: 400px; /* 编辑模式下增加最小宽度 */
}

.ai-message .message-content {
  background: transparent;
  border-radius: 0;
  padding: 8px 0;
  box-shadow: none;
  border: none;
  transition: all 0.3s ease;
  width: 100%; /* 占满父容器宽度，避免思考折叠时宽度变化 */
  min-width: 210px; /* 确保有足够宽度包含加载文字 */
}

.ai-message .message-content:hover {
  box-shadow: none;
  transform: none;
}

.user-message .message-content {
  background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 0%);
  color: #2d3748;
  border-radius: 24px 24px 24px 24px;
  padding: 2px 12px;
  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(102, 126, 234, 0.1); */
  transition: all 0.3s ease;
}

.user-message .message-content:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.image-message .message-content {
  background: #f5f5f5;
  color: #333;
  border-radius: 24px 24px 24px 8px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
}

.message-text {
  font-size: 15px;
  line-height: 1.4;
  word-wrap: break-word;
  margin-bottom: 0;
  font-weight: 400;
}

.user-message .message-text {
  color: #2d3748;
}

.ai-message .message-text {
  color: #2d3748;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-content {
    max-width: 85%;
  }

  .message-content.editing-mode {
    max-width: 95%;
    min-width: 300px;
  }

  .ai-message .message-content {
    padding: 8px 0;
    border-radius: 0;
  }

  .user-message .message-content {
    padding: 6px 10px;
    border-radius: 20px 20px 6px 20px;
  }

  .message-text {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .message-content {
    max-width: 90%;
  }

  .message-content.editing-mode {
    max-width: 98%;
    min-width: 280px;
  }

  .ai-message .message-content {
    padding: 8px 0;
  }

  .user-message .message-content {
    padding: 6px 10px;
  }
}

/* ===== 深色主题适配 ===== */
[data-theme="dark"] .ai-message .message-content {
  background: transparent;
  border: none;
  color: var(--app-text-primary);
}

[data-theme="dark"] .ai-message .message-content:hover {
  box-shadow: none;
}

[data-theme="dark"] .user-message .message-content {
  background: linear-gradient(135deg, #2d2d2d 0%, #1e1e1e 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--app-text-primary);
}

[data-theme="dark"] .user-message .message-content:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .image-message .message-content {
  background: var(--app-bg-tertiary);
  border: 1px solid var(--app-border-color);
  color: var(--app-text-primary);
}

[data-theme="dark"] .user-message .message-text,
[data-theme="dark"] .ai-message .message-text {
  color: var(--app-text-primary);
}
</style>
